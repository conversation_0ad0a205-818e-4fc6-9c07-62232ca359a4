#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import json
import os
from PyQt5.QtWidgets import QApplication, QMainWindow, QStackedWidget
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QFont

# Import GUI modules
from gui.login import LoginWindow
from gui.dashboard import DashboardWindow
from gui.tables import TablesWindow
from gui.menu import MenuWindow
from gui.takeaway import TakeawayWindow
from gui.delivery import DeliveryWindow
from gui.invoices import InvoicesWindow
from gui.sales import SalesWindow

# Import utilities
from utils.api_client import APIClient

class RestaurantApp(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("نظام فوترة المطعم - النوفوس")
        self.setGeometry(50, 50, 1600, 1000)
        self.setMinimumSize(1400, 900)
        
        # Load configuration
        self.load_config()
        
        # Initialize API client
        self.api_client = APIClient(self.config['server']['base_url'])
        
        # Current user info
        self.current_user = None

        # Delivery info
        self.delivery_phone = None
        self.delivery_address = None

        # Editing invoice
        self.editing_invoice = None

        # Set up UI
        self.setup_ui()
        self.setup_styles()
        
        # Show login window
        self.show_login()
    
    def load_config(self):
        """Load application configuration"""
        try:
            with open('config.json', 'r', encoding='utf-8') as f:
                self.config = json.load(f)
        except FileNotFoundError:
            # Default configuration
            self.config = {
                'server': {
                    'host': 'localhost',
                    'port': 3002,
                    'base_url': 'http://localhost:3002/api'
                },
                'app': {
                    'name': 'نظام فوترة المطعم',
                    'version': '1.0.0'
                },
                'ui': {
                    'primary_color': '#1E2A38',
                    'secondary_color': '#D4AF37',
                    'background_color': '#F5F5F5'
                }
            }
    
    def setup_ui(self):
        """Set up the main UI"""
        # Create stacked widget for different views
        self.stacked_widget = QStackedWidget()
        self.setCentralWidget(self.stacked_widget)
        
        # Initialize windows
        self.login_window = LoginWindow(self)
        self.dashboard_window = DashboardWindow(self)
        self.tables_window = TablesWindow(self)
        self.menu_window = MenuWindow(self)
        self.takeaway_window = TakeawayWindow(self)
        self.delivery_window = DeliveryWindow(self)
        self.invoices_window = InvoicesWindow(self)
        self.sales_window = SalesWindow(self)
        
        # Add windows to stack
        self.stacked_widget.addWidget(self.login_window)
        self.stacked_widget.addWidget(self.dashboard_window)
        self.stacked_widget.addWidget(self.tables_window)
        self.stacked_widget.addWidget(self.menu_window)
        self.stacked_widget.addWidget(self.takeaway_window)
        self.stacked_widget.addWidget(self.delivery_window)
        self.stacked_widget.addWidget(self.invoices_window)
        self.stacked_widget.addWidget(self.sales_window)
    
    def setup_styles(self):
        """Set up application styles"""
        # Set Arabic font with better sizing
        font = QFont("Arial", 11)
        font.setBold(False)
        self.setFont(font)

        # Apply responsive stylesheet
        style = f"""
        QMainWindow {{
            background-color: {self.config['ui']['background_color']};
            font-family: Arial;
        }}

        QPushButton {{
            background-color: {self.config['ui']['primary_color']};
            color: white;
            border: none;
            padding: 8px 16px;
            margin: 2px;
            border-radius: 6px;
            font-size: 12px;
            font-weight: bold;
            min-height: 20px;
        }}

        QPushButton:hover {{
            background-color: {self.config['ui']['secondary_color']};
        }}

        QPushButton:pressed {{
            background-color: #B8941F;
        }}

        QLineEdit {{
            padding: 6px 8px;
            border: 2px solid #ddd;
            border-radius: 4px;
            font-size: 12px;
            min-height: 16px;
            background-color: white;
        }}

        QLineEdit:focus {{
            border-color: {self.config['ui']['primary_color']};
        }}

        QLabel {{
            color: {self.config['ui']['primary_color']};
            font-weight: bold;
            font-size: 12px;
            padding: 2px;
        }}

        QTableWidget {{
            gridline-color: #ddd;
            background-color: white;
            alternate-background-color: #f9f9f9;
            font-size: 11px;
        }}

        QTableWidget::item {{
            padding: 6px;
            border-bottom: 1px solid #eee;
        }}

        QHeaderView::section {{
            background-color: {self.config['ui']['primary_color']};
            color: white;
            padding: 8px;
            border: none;
            font-weight: bold;
            font-size: 11px;
        }}

        QListWidget {{
            font-size: 11px;
            border: 1px solid #ddd;
        }}

        QListWidget::item {{
            padding: 4px;
            border-bottom: 1px solid #eee;
        }}

        QTextEdit {{
            font-size: 11px;
            padding: 4px;
        }}

        QComboBox {{
            font-size: 11px;
            padding: 4px;
        }}
        """
        
        self.setStyleSheet(style)
    
    def show_login(self):
        """Show login window"""
        self.stacked_widget.setCurrentWidget(self.login_window)
    
    def show_dashboard(self):
        """Show main dashboard"""
        self.stacked_widget.setCurrentWidget(self.dashboard_window)
    
    def show_tables(self):
        """Show tables view"""
        self.stacked_widget.setCurrentWidget(self.tables_window)
        self.tables_window.refresh_data()
    
    def show_menu(self, order_type='dine-in', hall_id=None, table_number=None):
        """Show menu view"""
        self.menu_window.set_order_context(order_type, hall_id, table_number)
        self.stacked_widget.setCurrentWidget(self.menu_window)
        self.menu_window.refresh_data()
    
    def show_takeaway(self):
        """Show takeaway view"""
        self.stacked_widget.setCurrentWidget(self.takeaway_window)
    
    def show_delivery(self):
        """Show delivery view"""
        self.stacked_widget.setCurrentWidget(self.delivery_window)
    
    def show_invoices(self):
        """Show invoices view"""
        self.stacked_widget.setCurrentWidget(self.invoices_window)
        self.invoices_window.refresh_data()
    
    def show_sales(self):
        """Show sales summary view"""
        self.stacked_widget.setCurrentWidget(self.sales_window)
        self.sales_window.refresh_data()
    
    def login_success(self, user_data, token):
        """Handle successful login"""
        self.current_user = user_data
        self.api_client.set_token(token)
        self.show_dashboard()
    
    def logout(self):
        """Handle logout"""
        self.api_client.logout()
        self.current_user = None
        self.show_login()

def main():
    app = QApplication(sys.argv)
    
    # Set application properties
    app.setApplicationName("Restaurant Billing System")
    app.setApplicationVersion("1.0.0")
    app.setOrganizationName("Novos Restaurant")
    
    # Set layout direction for Arabic
    app.setLayoutDirection(Qt.RightToLeft)
    
    # Create and show main window
    window = RestaurantApp()
    window.show()
    
    sys.exit(app.exec_())

if __name__ == '__main__':
    main()
