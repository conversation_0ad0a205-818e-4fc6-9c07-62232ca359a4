#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام إدارة مطعم النوفوس
Restaurant Management System - Novos

تطوير: شركة الباش البرمجية
Developer: Al-Bash Software Company
الهاتف/Phone: 07715086335
واتساب/WhatsApp: 07715086335
البريد الإلكتروني/Email: <EMAIL>
الموقع/Website: www.albash-software.com

© 2024 شركة الباش البرمجية. جميع الحقوق محفوظة.
© 2024 Al-Bash Software Company. All rights reserved.
"""

import sys
import json
import os
import time
from PyQt5.QtWidgets import QApplication, QMainWindow, QStackedWidget, QMessageBox
from PyQt5.QtCore import Qt, QTimer, QThread, pyqtSignal
from PyQt5.QtGui import QFont

# Import GUI modules
from gui.login import LoginWindow
from gui.dashboard import DashboardWindow
from gui.tables import TablesWindow
from gui.menu import MenuWindow
from gui.takeaway import TakeawayWindow
from gui.delivery import DeliveryWindow
from gui.invoices import InvoicesWindow
from gui.sales import SalesWindow
from gui.pending_orders import PendingOrdersWindow
from gui.about import AboutDialog, SplashScreen

# Import utilities
from utils.api_client import APIClient

class AppLoader(QThread):
    """Thread for loading application components"""
    progress = pyqtSignal(str)
    finished = pyqtSignal()

    def run(self):
        """Load application components with progress updates"""
        steps = [
            ("تحميل واجهة تسجيل الدخول...", 0.5),
            ("تحميل الشاشة الرئيسية...", 0.5),
            ("تحميل نظام الطاولات...", 0.3),
            ("تحميل نظام السفري...", 0.3),
            ("تحميل نظام الدلفري...", 0.3),
            ("تحميل نظام القوائم...", 0.4),
            ("تحميل نظام الفواتير...", 0.3),
            ("تحميل نظام المبيعات...", 0.3),
            ("تحميل نظام النوادل...", 0.4),
            ("تحميل قاعدة البيانات...", 0.6),
            ("تحميل إعدادات الطباعة...", 0.4),
            ("إنهاء التحميل...", 0.3)
        ]

        for step_text, delay in steps:
            self.progress.emit(step_text)
            time.sleep(delay)

        self.finished.emit()

class RestaurantApp(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("نظام فوترة المطعم - النوفوس")
        self.setGeometry(50, 50, 1600, 1000)
        self.setMinimumSize(1400, 900)
        
        # Load configuration
        self.load_config()
        
        # Initialize API client
        self.api_client = APIClient(self.config['server']['base_url'])
        
        # Current user info
        self.current_user = None

        # Delivery info
        self.delivery_phone = None
        self.delivery_address = None

        # Editing invoice
        self.editing_invoice = None

        # Set up UI
        self.setup_ui()
        self.setup_styles()
        
        # Initialize notification system
        self.pending_orders_count = 0
        self.notification_timer = QTimer()
        self.notification_timer.timeout.connect(self.check_pending_orders)
        self.notification_timer.start(30000)  # Check every 30 seconds

        # Show login window
        self.show_login()
    
    def load_config(self):
        """Load application configuration"""
        try:
            with open('config.json', 'r', encoding='utf-8') as f:
                self.config = json.load(f)
        except FileNotFoundError:
            # Default configuration
            self.config = {
                'server': {
                    'host': 'localhost',
                    'port': 3002,
                    'base_url': 'http://localhost:3002/api'
                },
                'app': {
                    'name': 'نظام فوترة المطعم',
                    'version': '1.0.0'
                },
                'ui': {
                    'primary_color': '#1E2A38',
                    'secondary_color': '#D4AF37',
                    'background_color': '#F5F5F5'
                }
            }
    
    def setup_ui(self):
        """Set up the main UI"""
        # Create stacked widget for different views
        self.stacked_widget = QStackedWidget()
        self.setCentralWidget(self.stacked_widget)
        
        # Initialize windows
        self.login_window = LoginWindow(self)
        self.dashboard_window = DashboardWindow(self)
        self.tables_window = TablesWindow(self)
        self.menu_window = MenuWindow(self)
        self.takeaway_window = TakeawayWindow(self)
        self.delivery_window = DeliveryWindow(self)
        self.invoices_window = InvoicesWindow(self)
        self.sales_window = SalesWindow(self)
        
        # Add windows to stack
        self.stacked_widget.addWidget(self.login_window)
        self.stacked_widget.addWidget(self.dashboard_window)
        self.stacked_widget.addWidget(self.tables_window)
        self.stacked_widget.addWidget(self.menu_window)
        self.stacked_widget.addWidget(self.takeaway_window)
        self.stacked_widget.addWidget(self.delivery_window)
        self.stacked_widget.addWidget(self.invoices_window)
        self.stacked_widget.addWidget(self.sales_window)
    
    def setup_styles(self):
        """Set up application styles"""
        # Set Arabic font with better sizing
        font = QFont("Arial", 11)
        font.setBold(False)
        self.setFont(font)

        # Apply responsive stylesheet
        style = f"""
        QMainWindow {{
            background-color: {self.config['ui']['background_color']};
            font-family: Arial;
        }}

        QPushButton {{
            background-color: {self.config['ui']['primary_color']};
            color: white;
            border: none;
            padding: 8px 16px;
            margin: 2px;
            border-radius: 6px;
            font-size: 12px;
            font-weight: bold;
            min-height: 20px;
        }}

        QPushButton:hover {{
            background-color: {self.config['ui']['secondary_color']};
        }}

        QPushButton:pressed {{
            background-color: #B8941F;
        }}

        QLineEdit {{
            padding: 6px 8px;
            border: 2px solid #ddd;
            border-radius: 4px;
            font-size: 12px;
            min-height: 16px;
            background-color: white;
        }}

        QLineEdit:focus {{
            border-color: {self.config['ui']['primary_color']};
        }}

        QLabel {{
            color: {self.config['ui']['primary_color']};
            font-weight: bold;
            font-size: 12px;
            padding: 2px;
        }}

        QTableWidget {{
            gridline-color: #ddd;
            background-color: white;
            alternate-background-color: #f9f9f9;
            font-size: 11px;
        }}

        QTableWidget::item {{
            padding: 6px;
            border-bottom: 1px solid #eee;
        }}

        QHeaderView::section {{
            background-color: {self.config['ui']['primary_color']};
            color: white;
            padding: 8px;
            border: none;
            font-weight: bold;
            font-size: 11px;
        }}

        QListWidget {{
            font-size: 11px;
            border: 1px solid #ddd;
        }}

        QListWidget::item {{
            padding: 4px;
            border-bottom: 1px solid #eee;
        }}

        QTextEdit {{
            font-size: 11px;
            padding: 4px;
        }}

        QComboBox {{
            font-size: 11px;
            padding: 4px;
        }}
        """
        
        self.setStyleSheet(style)
    
    def show_login(self):
        """Show login window"""
        self.stacked_widget.setCurrentWidget(self.login_window)
    
    def show_dashboard(self):
        """Show main dashboard"""
        self.stacked_widget.setCurrentWidget(self.dashboard_window)
    
    def show_tables(self):
        """Show tables view"""
        self.stacked_widget.setCurrentWidget(self.tables_window)
        self.tables_window.refresh_data()
    
    def show_menu(self, order_type='dine-in', hall_id=None, table_number=None):
        """Show menu view"""
        self.menu_window.set_order_context(order_type, hall_id, table_number)
        self.stacked_widget.setCurrentWidget(self.menu_window)
        self.menu_window.refresh_data()
    
    def show_takeaway(self):
        """Show takeaway view"""
        self.stacked_widget.setCurrentWidget(self.takeaway_window)
    
    def show_delivery(self):
        """Show delivery view"""
        self.stacked_widget.setCurrentWidget(self.delivery_window)
    
    def show_invoices(self):
        """Show invoices view"""
        self.stacked_widget.setCurrentWidget(self.invoices_window)
        self.invoices_window.refresh_data()
    
    def show_sales(self):
        """Show sales summary view"""
        self.stacked_widget.setCurrentWidget(self.sales_window)
        self.sales_window.refresh_data()

    def show_pending_orders(self):
        """Show pending waiter orders"""
        from gui.pending_orders import PendingOrdersWindow

        # Create pending orders window if not exists
        if not hasattr(self, 'pending_orders_window'):
            self.pending_orders_window = PendingOrdersWindow(self)
            self.stacked_widget.addWidget(self.pending_orders_window)

        # Switch to pending orders view
        self.stacked_widget.setCurrentWidget(self.pending_orders_window)
        self.pending_orders_window.refresh_data()
    
    def login_success(self, user_data, token):
        """Handle successful login"""
        self.current_user = user_data
        self.api_client.set_token(token)
        self.show_dashboard()
    
    def logout(self):
        """Handle logout"""
        self.api_client.logout()
        self.current_user = None
        self.show_login()

    def check_pending_orders(self):
        """Check for pending waiter orders and update notification"""
        try:
            import requests
            response = requests.get('http://localhost:3001/api/waiter-orders/pending', timeout=5)
            if response.status_code == 200:
                data = response.json()
                count = len(data.get('orders', []))

                # Update notification badge if dashboard is visible
                if hasattr(self, 'dashboard_window') and self.dashboard_window:
                    self.dashboard_window.update_pending_orders_count(count)

                # Show notification if new orders arrived
                if count > self.pending_orders_count:
                    self.show_new_order_notification(count - self.pending_orders_count)

                self.pending_orders_count = count

        except Exception as e:
            print(f"Error checking pending orders: {e}")

    def show_new_order_notification(self, new_count):
        """Show notification for new orders"""
        from PyQt5.QtWidgets import QMessageBox

        # Check if any of the new orders are invoice edits
        try:
            import requests
            response = requests.get('http://localhost:3001/api/waiter-orders/pending', timeout=5)
            if response.status_code == 200:
                data = response.json()
                orders = data.get('orders', [])

                # Check for invoice edits
                invoice_edits = [order for order in orders if order.get('isInvoiceEdit')]
                regular_orders = [order for order in orders if not order.get('isInvoiceEdit')]

                if invoice_edits and regular_orders:
                    message = f"وصل {len(regular_orders)} طلب جديد و {len(invoice_edits)} طلب تعديل فاتورة!"
                elif invoice_edits:
                    if len(invoice_edits) == 1:
                        message = "وصل طلب تعديل فاتورة من النادل!"
                    else:
                        message = f"وصل {len(invoice_edits)} طلبات تعديل فواتير!"
                elif regular_orders:
                    if len(regular_orders) == 1:
                        message = "وصل طلب جديد من النادل!"
                    else:
                        message = f"وصل {len(regular_orders)} طلبات جديدة من النوادل!"
                else:
                    message = "وصلت طلبات جديدة من النوادل!"
            else:
                if new_count == 1:
                    message = "وصل طلب جديد من النادل!"
                else:
                    message = f"وصل {new_count} طلبات جديدة من النوادل!"
        except:
            if new_count == 1:
                message = "وصل طلب جديد من النادل!"
            else:
                message = f"وصل {new_count} طلبات جديدة من النوادل!"

        # Create notification message box
        msg = QMessageBox()
        msg.setWindowTitle("طلب جديد")
        msg.setText(message)
        msg.setIcon(QMessageBox.Information)
        msg.setStandardButtons(QMessageBox.Ok)
        msg.setDefaultButton(QMessageBox.Ok)

        # Show for 3 seconds then auto close
        QTimer.singleShot(3000, msg.close)
        msg.exec_()

def main():
    """Main application entry point"""
    app = QApplication(sys.argv)

    # Set application properties
    app.setApplicationName("نظام إدارة مطعم النوفوس")
    app.setApplicationVersion("1.0.0")
    app.setOrganizationName("شركة الباش البرمجية")
    app.setOrganizationDomain("www.albash-software.com")

    # Set layout direction for Arabic
    app.setLayoutDirection(Qt.RightToLeft)

    # Set default font
    font = QFont("Arial", 10)
    app.setFont(font)

    # Show splash screen
    splash = SplashScreen()
    splash.show()

    # Process events to show splash screen
    app.processEvents()

    # Create loader thread
    loader = AppLoader()
    loader.progress.connect(splash.showMessage)

    # Create main window
    window = RestaurantApp()

    # When loading is finished, show main window and close splash
    def on_loading_finished():
        splash.finish(window)
        window.show()

    loader.finished.connect(on_loading_finished)
    loader.start()

    sys.exit(app.exec_())

if __name__ == '__main__':
    main()
