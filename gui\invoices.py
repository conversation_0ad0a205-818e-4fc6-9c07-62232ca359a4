from PyQt5.QtWidgets import (QW<PERSON>t, QVBoxLayout, QHBoxLayout, QLabel,
                             QPushButton, QFrame, QTableWidget, QTableWidgetItem,
                             QMessageBox, QHeaderView, QComboBox, QDialog)
from PyQt5.QtCore import Qt, QThread, pyqtSignal
from PyQt5.QtGui import QFont

class InvoicesLoadThread(QThread):
    """Thread for loading invoices data"""
    data_loaded = pyqtSignal(dict)
    
    def __init__(self, api_client, filters=None):
        super().__init__()
        self.api_client = api_client
        self.filters = filters or {}
    
    def run(self):
        result = self.api_client.get_orders(**self.filters)
        self.data_loaded.emit(result)

class InvoicesWindow(QWidget):
    def __init__(self, main_app):
        super().__init__()
        self.main_app = main_app
        self.invoices_data = []
        self.setup_ui()
    
    def setup_ui(self):
        """Set up the invoices UI"""
        layout = QVBoxLayout()
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)
        
        # Top bar
        self.create_top_bar(layout)
        
        # Filters bar
        self.create_filters_bar(layout)
        
        # Invoices table
        self.create_invoices_table(layout)
        
        # Footer navigation
        self.create_footer_navigation(layout)
        
        self.setLayout(layout)
    
    def create_top_bar(self, parent_layout):
        """Create top navigation bar"""
        top_bar = QFrame()
        top_bar.setFixedHeight(80)
        top_bar.setStyleSheet("""
            QFrame {
                background-color: #1E2A38;
                border-bottom: 3px solid #D4AF37;
            }
        """)
        
        top_layout = QHBoxLayout(top_bar)
        top_layout.setContentsMargins(20, 10, 20, 10)
        
        # Notifications icon (left)
        notifications_btn = QPushButton("🔔")
        notifications_btn.setFixedSize(50, 50)
        notifications_btn.setStyleSheet("""
            QPushButton {
                background-color: transparent;
                color: #D4AF37;
                border: 2px solid #D4AF37;
                border-radius: 25px;
                font-size: 20px;
            }
            QPushButton:hover {
                background-color: #D4AF37;
                color: #1E2A38;
            }
        """)
        top_layout.addWidget(notifications_btn)
        
        # Center area
        center_widget = QWidget()
        center_layout = QVBoxLayout(center_widget)
        center_layout.setAlignment(Qt.AlignCenter)
        
        page_title = QLabel("الفواتير القديمة")
        page_title.setAlignment(Qt.AlignCenter)
        page_title.setFont(QFont("Arial", 18, QFont.Bold))
        page_title.setStyleSheet("color: white;")
        center_layout.addWidget(page_title)
        
        top_layout.addWidget(center_widget)
        
        # Settings icon (right)
        settings_btn = QPushButton("⚙️")
        settings_btn.setFixedSize(50, 50)
        settings_btn.setStyleSheet("""
            QPushButton {
                background-color: transparent;
                color: #D4AF37;
                border: 2px solid #D4AF37;
                border-radius: 25px;
                font-size: 20px;
            }
            QPushButton:hover {
                background-color: #D4AF37;
                color: #1E2A38;
            }
        """)
        top_layout.addWidget(settings_btn)
        
        parent_layout.addWidget(top_bar)
    
    def create_filters_bar(self, parent_layout):
        """Create filters bar"""
        filters_bar = QFrame()
        filters_bar.setFixedHeight(60)
        filters_bar.setStyleSheet("""
            QFrame {
                background-color: white;
                border-bottom: 2px solid #ddd;
            }
        """)
        
        filters_layout = QHBoxLayout(filters_bar)
        filters_layout.setContentsMargins(20, 10, 20, 10)
        filters_layout.setSpacing(15)
        
        # Type filter
        type_label = QLabel("النوع:")
        type_label.setFont(QFont("Arial", 12, QFont.Bold))
        filters_layout.addWidget(type_label)
        
        self.type_combo = QComboBox()
        self.type_combo.addItems(["الكل", "طاولات", "سفري", "دلفري"])
        self.type_combo.setStyleSheet("""
            QComboBox {
                padding: 5px;
                border: 2px solid #ddd;
                border-radius: 5px;
                min-width: 100px;
            }
        """)
        self.type_combo.currentTextChanged.connect(self.apply_filters)
        filters_layout.addWidget(self.type_combo)
        
        # Date filter
        date_label = QLabel("التاريخ:")
        date_label.setFont(QFont("Arial", 12, QFont.Bold))
        filters_layout.addWidget(date_label)
        
        self.date_combo = QComboBox()
        self.date_combo.addItems(["الكل", "اليوم", "أمس", "هذا الأسبوع"])
        self.date_combo.setStyleSheet("""
            QComboBox {
                padding: 5px;
                border: 2px solid #ddd;
                border-radius: 5px;
                min-width: 100px;
            }
        """)
        self.date_combo.currentTextChanged.connect(self.apply_filters)
        filters_layout.addWidget(self.date_combo)
        
        filters_layout.addStretch()
        
        # Refresh button
        refresh_btn = QPushButton("🔄 تحديث")
        refresh_btn.setFont(QFont("Arial", 12, QFont.Bold))
        refresh_btn.setStyleSheet("""
            QPushButton {
                background-color: #D4AF37;
                color: white;
                border: none;
                border-radius: 8px;
                padding: 8px 16px;
            }
            QPushButton:hover {
                background-color: #B8941F;
            }
        """)
        refresh_btn.clicked.connect(self.refresh_data)
        filters_layout.addWidget(refresh_btn)
        
        parent_layout.addWidget(filters_bar)
    
    def create_invoices_table(self, parent_layout):
        """Create invoices table"""
        self.invoices_table = QTableWidget()
        self.invoices_table.setColumnCount(7)
        self.invoices_table.setHorizontalHeaderLabels([
            "رقم الفاتورة", "النوع", "المجموع", "التاريخ", "النادل", "الحالة", "الإجراءات"
        ])
        
        # Set table properties
        header = self.invoices_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.Stretch)
        
        self.invoices_table.setAlternatingRowColors(True)
        self.invoices_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.invoices_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #ddd;
                background-color: white;
                alternate-background-color: #f9f9f9;
                border: 1px solid #ddd;
                border-radius: 6px;
            }
            QTableWidget::item {
                padding: 15px 10px;
                border-bottom: 3px solid #f0f0f0;
                font-size: 11px;
                min-height: 25px;
            }
            QTableWidget::item:selected {
                background-color: #e3f2fd;
            }
            QHeaderView::section {
                background-color: #1E2A38;
                color: white;
                padding: 10px 8px;
                border: none;
                font-weight: bold;
                font-size: 11px;
            }
        """)
        
        parent_layout.addWidget(self.invoices_table)
    
    def create_footer_navigation(self, parent_layout):
        """Create footer navigation bar"""
        footer = QFrame()
        footer.setFixedHeight(70)
        footer.setStyleSheet("""
            QFrame {
                background-color: #1E2A38;
                border-top: 2px solid #D4AF37;
            }
        """)
        
        footer_layout = QHBoxLayout(footer)
        footer_layout.setContentsMargins(20, 10, 20, 10)
        footer_layout.setSpacing(20)
        
        # Back button
        back_btn = QPushButton("🔙 العودة")
        back_btn.setFont(QFont("Arial", 12, QFont.Bold))
        back_btn.setStyleSheet("""
            QPushButton {
                background-color: transparent;
                color: #D4AF37;
                border: 2px solid #D4AF37;
                border-radius: 8px;
                padding: 8px 16px;
            }
            QPushButton:hover {
                background-color: #D4AF37;
                color: #1E2A38;
            }
        """)
        back_btn.clicked.connect(self.main_app.show_dashboard)
        footer_layout.addWidget(back_btn)
        
        footer_layout.addStretch()
        
        # Sales summary button
        sales_btn = QPushButton("📊 مبيعات اليوم")
        sales_btn.setFont(QFont("Arial", 12, QFont.Bold))
        sales_btn.setStyleSheet("""
            QPushButton {
                background-color: transparent;
                color: #D4AF37;
                border: 2px solid #D4AF37;
                border-radius: 8px;
                padding: 8px 16px;
            }
            QPushButton:hover {
                background-color: #D4AF37;
                color: #1E2A38;
            }
        """)
        sales_btn.clicked.connect(self.main_app.show_sales)
        footer_layout.addWidget(sales_btn)
        
        parent_layout.addWidget(footer)
    
    def refresh_data(self):
        """Refresh invoices data"""
        filters = self.get_current_filters()
        self.load_thread = InvoicesLoadThread(self.main_app.api_client, filters)
        self.load_thread.data_loaded.connect(self.handle_data_loaded)
        self.load_thread.start()
    
    def get_current_filters(self):
        """Get current filter values"""
        filters = {}
        
        # Type filter
        type_text = self.type_combo.currentText()
        if type_text == "طاولات":
            filters['type'] = 'dine-in'
        elif type_text == "سفري":
            filters['type'] = 'takeaway'
        elif type_text == "دلفري":
            filters['type'] = 'delivery'
        
        # Date filter
        date_text = self.date_combo.currentText()
        if date_text == "اليوم":
            filters['date'] = 'today'
        
        return filters
    
    def apply_filters(self):
        """Apply current filters"""
        self.refresh_data()
    
    def handle_data_loaded(self, result):
        """Handle loaded invoices data"""
        if 'error' in result:
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل الفواتير: {result['error']}")
            return
        
        if 'orders' in result:
            self.invoices_data = result['orders']
            self.update_invoices_table()
    
    def update_invoices_table(self):
        """Update invoices table"""
        self.invoices_table.setRowCount(len(self.invoices_data))
        
        for row, invoice in enumerate(self.invoices_data):
            # Order number (with modification indicator)
            order_number = str(invoice.get('orderNumber', ''))
            if invoice.get('isModified'):
                order_number += " ✏️"
            self.invoices_table.setItem(row, 0, QTableWidgetItem(order_number))

            # Type
            order_type = invoice.get('type', '')
            type_text = {'dine-in': 'طاولة', 'takeaway': 'سفري', 'delivery': 'دلفري'}.get(order_type, order_type)
            if invoice.get('isModified'):
                type_text += " (معدل)"
            self.invoices_table.setItem(row, 1, QTableWidgetItem(type_text))
            
            # Total amount (Iraqi format)
            total = int(invoice.get('totalAmount', 0))
            formatted_total = f"{total:,}".replace(',', '.')
            self.invoices_table.setItem(row, 2, QTableWidgetItem(f"{formatted_total} دينار"))
            
            # Date
            created_at = invoice.get('createdAt', '')
            if created_at:
                date_str = created_at.split('T')[0]  # Extract date part
                self.invoices_table.setItem(row, 3, QTableWidgetItem(date_str))
            
            # Waiter
            self.invoices_table.setItem(row, 4, QTableWidgetItem(invoice.get('waiterName', '')))
            
            # Status
            status = invoice.get('status', '')
            status_text = {'pending': 'معلق', 'preparing': 'قيد التحضير', 'ready': 'جاهز', 'completed': 'مكتمل', 'cancelled': 'ملغي'}.get(status, status)
            self.invoices_table.setItem(row, 5, QTableWidgetItem(status_text))
            
            # Actions - Create action buttons
            actions_widget = QWidget()
            actions_layout = QHBoxLayout(actions_widget)
            actions_layout.setContentsMargins(5, 5, 5, 5)
            actions_layout.setSpacing(5)
            
            # View button
            view_btn = QPushButton("عرض")
            view_btn.setFont(QFont("Arial", 8))
            view_btn.setToolTip("عرض المحتويات")
            view_btn.setStyleSheet("""
                QPushButton {
                    background-color: #17a2b8;
                    color: white;
                    border: none;
                    border-radius: 4px;
                    font-size: 8px;
                    font-weight: bold;
                    padding: 4px 8px;
                    min-width: 30px;
                }
                QPushButton:hover {
                    background-color: #138496;
                }
            """)
            view_btn.clicked.connect(lambda checked, inv=invoice: self.view_invoice(inv))
            actions_layout.addWidget(view_btn)

            # Edit button
            edit_btn = QPushButton("تعديل")
            edit_btn.setFont(QFont("Arial", 8))
            edit_btn.setToolTip("تعديل الطلب")
            edit_btn.setStyleSheet("""
                QPushButton {
                    background-color: #ffc107;
                    color: #212529;
                    border: none;
                    border-radius: 4px;
                    font-size: 8px;
                    font-weight: bold;
                    padding: 4px 8px;
                    min-width: 30px;
                }
                QPushButton:hover {
                    background-color: #e0a800;
                }
            """)
            edit_btn.clicked.connect(lambda checked, inv=invoice: self.edit_invoice(inv))
            actions_layout.addWidget(edit_btn)

            # Print button
            print_btn = QPushButton("طباعة")
            print_btn.setFont(QFont("Arial", 8))
            print_btn.setToolTip("طباعة")
            print_btn.setStyleSheet("""
                QPushButton {
                    background-color: #28a745;
                    color: white;
                    border: none;
                    border-radius: 4px;
                    font-size: 8px;
                    font-weight: bold;
                    padding: 4px 8px;
                    min-width: 30px;
                }
                QPushButton:hover {
                    background-color: #218838;
                }
            """)
            print_btn.clicked.connect(lambda checked, inv=invoice: self.print_invoice(inv))
            actions_layout.addWidget(print_btn)

            # Delete button
            delete_btn = QPushButton("حذف")
            delete_btn.setFont(QFont("Arial", 8))
            delete_btn.setToolTip("حذف")
            delete_btn.setStyleSheet("""
                QPushButton {
                    background-color: #dc3545;
                    color: white;
                    border: none;
                    border-radius: 4px;
                    font-size: 8px;
                    font-weight: bold;
                    padding: 4px 8px;
                    min-width: 30px;
                }
                QPushButton:hover {
                    background-color: #c82333;
                }
            """)
            delete_btn.clicked.connect(lambda checked, inv=invoice: self.delete_invoice(inv))
            actions_layout.addWidget(delete_btn)
            
            self.invoices_table.setCellWidget(row, 6, actions_widget)
    
    def print_invoice(self, invoice):
        """Print invoice"""
        result = self.main_app.api_client.print_receipt(invoice['id'])
        if 'error' in result:
            QMessageBox.critical(self, "خطأ", f"فشل في طباعة الفاتورة: {result['error']}")
        else:
            QMessageBox.information(self, "نجح", "تم طباعة الفاتورة بنجاح")
    
    def view_invoice(self, invoice):
        """View invoice contents"""
        from gui.invoice_viewer import InvoiceViewerDialog
        viewer = InvoiceViewerDialog(invoice, self)
        viewer.exec_()

    def edit_invoice(self, invoice):
        """Edit invoice - open in menu view for editing"""
        # Set the invoice data for editing
        self.main_app.editing_invoice = invoice

        # Navigate to menu view based on order type
        order_type = invoice.get('type', 'dine-in')
        if order_type == 'dine-in':
            self.main_app.show_menu('dine-in', invoice.get('hallId'), invoice.get('tableNumber'))
        elif order_type == 'takeaway':
            self.main_app.show_menu('takeaway')
        elif order_type == 'delivery':
            # Set delivery info
            self.main_app.delivery_phone = invoice.get('customerPhone', '')
            self.main_app.delivery_address = invoice.get('customerAddress', '')
            self.main_app.show_menu('delivery')

        # The menu window will detect editing_invoice and load the data

    def delete_invoice(self, invoice):
        """Delete invoice"""
        reply = QMessageBox.question(self, "تأكيد الحذف",
                                   f"هل تريد حذف الفاتورة رقم {invoice.get('orderNumber', '')}؟",
                                   QMessageBox.Yes | QMessageBox.No)

        if reply == QMessageBox.Yes:
            result = self.main_app.api_client.delete_order(invoice['id'])
            if 'error' in result:
                QMessageBox.critical(self, "خطأ", f"فشل في حذف الفاتورة: {result['error']}")
            else:
                QMessageBox.information(self, "نجح", "تم حذف الفاتورة بنجاح")
                self.refresh_data()
