from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                             QPushButton, QFrame, QGridLayout)
from PyQt5.QtCore import Qt, QTimer, QDateTime
from PyQt5.QtGui import <PERSON><PERSON>ont, QIcon

class DashboardWindow(QWidget):
    def __init__(self, main_app):
        super().__init__()
        self.main_app = main_app
        self.setup_ui()
        
        # Timer for updating time
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_time)
        self.timer.start(1000)  # Update every second
    
    def setup_ui(self):
        """Set up the dashboard UI"""
        layout = QVBoxLayout()
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)
        
        # Top bar
        self.create_top_bar(layout)
        
        # Main content area
        self.create_main_content(layout)
        
        # Footer navigation
        self.create_footer_navigation(layout)
        
        self.setLayout(layout)
    
    def create_top_bar(self, parent_layout):
        """Create top navigation bar"""
        top_bar = QFrame()
        top_bar.setFixedHeight(80)
        top_bar.setStyleSheet("""
            QFrame {
                background-color: #1E2A38;
                border-bottom: 3px solid #D4AF37;
            }
        """)
        
        top_layout = QHBoxLayout(top_bar)
        top_layout.setContentsMargins(20, 10, 20, 10)
        
        # Notifications icon (left)
        notifications_btn = QPushButton("🔔")
        notifications_btn.setFixedSize(50, 50)
        notifications_btn.setStyleSheet("""
            QPushButton {
                background-color: transparent;
                color: #D4AF37;
                border: 2px solid #D4AF37;
                border-radius: 25px;
                font-size: 20px;
            }
            QPushButton:hover {
                background-color: #D4AF37;
                color: #1E2A38;
            }
        """)
        top_layout.addWidget(notifications_btn)
        
        # Center area - App name and time
        center_widget = QWidget()
        center_layout = QVBoxLayout(center_widget)
        center_layout.setAlignment(Qt.AlignCenter)
        center_layout.setSpacing(5)
        
        app_name = QLabel("نظام فوترة المطعم - النوفوس")
        app_name.setAlignment(Qt.AlignCenter)
        app_name.setFont(QFont("Arial", 18, QFont.Bold))
        app_name.setStyleSheet("color: white;")
        center_layout.addWidget(app_name)
        
        self.time_label = QLabel()
        self.time_label.setAlignment(Qt.AlignCenter)
        self.time_label.setFont(QFont("Arial", 12))
        self.time_label.setStyleSheet("color: #D4AF37;")
        center_layout.addWidget(self.time_label)
        
        top_layout.addWidget(center_widget)

        # Pending orders button with notification badge
        pending_container = QWidget()
        pending_container.setFixedSize(80, 60)

        self.pending_orders_btn = QPushButton("👨‍🍳")
        self.pending_orders_btn.setParent(pending_container)
        self.pending_orders_btn.setGeometry(0, 0, 60, 60)
        self.pending_orders_btn.setStyleSheet("""
            QPushButton {
                background-color: transparent;
                color: #D4AF37;
                border: 3px solid #D4AF37;
                border-radius: 30px;
                font-size: 28px;
            }
            QPushButton:hover {
                background-color: #D4AF37;
                color: #1E2A38;
            }
        """)
        self.pending_orders_btn.clicked.connect(self.main_app.show_pending_orders)

        # Notification badge for pending orders
        self.notification_badge = QLabel("0")
        self.notification_badge.setParent(pending_container)
        self.notification_badge.setGeometry(45, 5, 30, 25)
        self.notification_badge.setAlignment(Qt.AlignCenter)
        self.notification_badge.setStyleSheet("""
            QLabel {
                background-color: #dc3545;
                color: white;
                border-radius: 12px;
                font-size: 14px;
                font-weight: bold;
                padding: 3px;
                min-width: 20px;
            }
        """)
        self.notification_badge.hide()

        top_layout.addWidget(pending_container)

        # Settings icon (right)
        settings_btn = QPushButton("⚙️")
        settings_btn.setFixedSize(50, 50)
        settings_btn.setStyleSheet("""
            QPushButton {
                background-color: transparent;
                color: #D4AF37;
                border: 2px solid #D4AF37;
                border-radius: 25px;
                font-size: 20px;
            }
            QPushButton:hover {
                background-color: #D4AF37;
                color: #1E2A38;
            }
        """)
        settings_btn.clicked.connect(self.show_settings)
        top_layout.addWidget(settings_btn)
        
        parent_layout.addWidget(top_bar)
        
        # Update time initially
        self.update_time()
    
    def create_main_content(self, parent_layout):
        """Create main content area with three buttons"""
        main_content = QWidget()
        main_layout = QVBoxLayout(main_content)
        main_layout.setAlignment(Qt.AlignCenter)
        main_layout.setSpacing(40)
        main_layout.setContentsMargins(50, 50, 50, 50)
        
        # Welcome message
        if self.main_app.current_user:
            welcome_msg = f"مرحباً، {self.main_app.current_user.get('name', 'المستخدم')}"
        else:
            welcome_msg = "مرحباً بك في نظام فوترة المطعم"
        
        welcome_label = QLabel(welcome_msg)
        welcome_label.setAlignment(Qt.AlignCenter)
        welcome_label.setFont(QFont("Arial", 16))
        welcome_label.setStyleSheet("color: #1E2A38; margin-bottom: 30px;")
        main_layout.addWidget(welcome_label)
        
        # Three main buttons
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(40)
        buttons_layout.setAlignment(Qt.AlignCenter)
        
        # Tables button
        tables_btn = self.create_main_button("الطاولات", "🍽️", self.main_app.show_tables)
        buttons_layout.addWidget(tables_btn)
        
        # Takeaway button
        takeaway_btn = self.create_main_button("السفري", "🥡", self.main_app.show_takeaway)
        buttons_layout.addWidget(takeaway_btn)
        
        # Delivery button
        delivery_btn = self.create_main_button("الدلفري", "🚗", self.main_app.show_delivery)
        buttons_layout.addWidget(delivery_btn)

        main_layout.addLayout(buttons_layout)
        
        parent_layout.addWidget(main_content)
    
    def create_main_button(self, text, icon, callback):
        """Create a responsive main dashboard button"""
        button = QPushButton()
        button.clicked.connect(callback)

        # Get screen size for responsive design
        screen = QApplication.primaryScreen()
        screen_size = screen.size()
        screen_width = screen_size.width()
        screen_height = screen_size.height()

        # Calculate responsive sizes
        if screen_width < 800:  # Small screens (tablets/phones)
            button_width = 200
            button_height = 160
            font_size = 14
            icon_size = 32
            padding = 10
        elif screen_width < 1200:  # Medium screens
            button_width = 250
            button_height = 200
            font_size = 16
            icon_size = 40
            padding = 15
        else:  # Large screens
            button_width = 300
            button_height = 250
            font_size = 20
            icon_size = 48
            padding = 20

        button.setFixedSize(button_width, button_height)
        button.setText(f"{icon}\n{text}")
        button.setFont(QFont("Arial", font_size, QFont.Bold))

        button.setStyleSheet(f"""
            QPushButton {{
                background-color: #1E2A38;
                color: white;
                border: none;
                border-radius: 20px;
                font-size: {font_size}px;
                font-weight: bold;
                padding: {padding}px;
                text-align: center;
                line-height: 1.3;
            }}
            QPushButton:hover {{
                background-color: #D4AF37;
            }}
            QPushButton:pressed {{
                background-color: #B8941F;
            }}
        """)

        return button
    
    def create_footer_navigation(self, parent_layout):
        """Create footer navigation bar"""
        footer = QFrame()
        footer.setFixedHeight(70)
        footer.setStyleSheet("""
            QFrame {
                background-color: #1E2A38;
                border-top: 2px solid #D4AF37;
            }
        """)
        
        footer_layout = QHBoxLayout(footer)
        footer_layout.setContentsMargins(20, 10, 20, 10)
        footer_layout.setSpacing(20)
        
        # Back button
        back_btn = QPushButton("🔙 العودة")
        back_btn.setFont(QFont("Arial", 12, QFont.Bold))
        back_btn.setStyleSheet("""
            QPushButton {
                background-color: transparent;
                color: #D4AF37;
                border: 2px solid #D4AF37;
                border-radius: 8px;
                padding: 8px 16px;
            }
            QPushButton:hover {
                background-color: #D4AF37;
                color: #1E2A38;
            }
        """)
        back_btn.clicked.connect(self.main_app.logout)
        footer_layout.addWidget(back_btn)
        
        # Spacer
        footer_layout.addStretch()
        
        # Old invoices button
        invoices_btn = QPushButton("📜 عرض الفواتير القديمة")
        invoices_btn.setFont(QFont("Arial", 12, QFont.Bold))
        invoices_btn.setStyleSheet("""
            QPushButton {
                background-color: transparent;
                color: #D4AF37;
                border: 2px solid #D4AF37;
                border-radius: 8px;
                padding: 8px 16px;
            }
            QPushButton:hover {
                background-color: #D4AF37;
                color: #1E2A38;
            }
        """)
        invoices_btn.clicked.connect(self.main_app.show_invoices)
        footer_layout.addWidget(invoices_btn)
        
        # Sales summary button
        sales_btn = QPushButton("📊 مبيعات اليوم")
        sales_btn.setFont(QFont("Arial", 12, QFont.Bold))
        sales_btn.setStyleSheet("""
            QPushButton {
                background-color: transparent;
                color: #D4AF37;
                border: 2px solid #D4AF37;
                border-radius: 8px;
                padding: 8px 16px;
            }
            QPushButton:hover {
                background-color: #D4AF37;
                color: #1E2A38;
            }
        """)
        sales_btn.clicked.connect(self.main_app.show_sales)
        footer_layout.addWidget(sales_btn)
        
        parent_layout.addWidget(footer)
    
    def update_time(self):
        """Update the time display"""
        current_time = QDateTime.currentDateTime()
        time_str = current_time.toString("yyyy-MM-dd hh:mm:ss")
        self.time_label.setText(time_str)
    
    def show_settings(self):
        """Show settings dialog"""
        from gui.settings import SettingsDialog
        settings_dialog = SettingsDialog(self.main_app)
        settings_dialog.exec_()

    def update_pending_orders_count(self, count):
        """Update the pending orders notification badge"""
        if count > 0:
            self.notification_badge.setText(str(count))
            self.notification_badge.show()
        else:
            self.notification_badge.hide()
