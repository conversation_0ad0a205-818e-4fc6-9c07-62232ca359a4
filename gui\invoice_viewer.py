from PyQt5.QtWidgets import (Q<PERSON>ialog, QVBoxLayout, QHBoxLayout, QLabel, 
                             QPushButton, QFrame, QTableWidget, QTableWidgetItem,
                             QHeaderView, QTextEdit)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont
import datetime

class InvoiceViewerDialog(QDialog):
    def __init__(self, invoice, parent=None):
        super().__init__(parent)
        self.invoice = invoice
        self.setWindowTitle(f"عرض الفاتورة رقم {invoice.get('orderNumber', '')}")
        self.setFixedSize(600, 700)
        self.setModal(True)
        self.setup_ui()
    
    def setup_ui(self):
        """Set up the invoice viewer UI"""
        layout = QVBoxLayout()
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)
        
        # Title
        title = QLabel(f"فاتورة رقم {self.invoice.get('orderNumber', '')}")
        title.setAlignment(Qt.AlignCenter)
        title.setFont(QFont("Arial", 18, QFont.Bold))
        title.setStyleSheet("color: #1E2A38; margin-bottom: 20px;")
        layout.addWidget(title)
        
        # Invoice info
        info_frame = QFrame()
        info_frame.setStyleSheet("""
            QFrame {
                background-color: #f8f9fa;
                border: 2px solid #ddd;
                border-radius: 8px;
                padding: 15px;
            }
        """)
        info_layout = QVBoxLayout(info_frame)
        
        # Basic info
        created_at = self.invoice.get('createdAt', '')
        if created_at:
            try:
                date_obj = datetime.datetime.fromisoformat(created_at.replace('Z', '+00:00'))
                formatted_date = date_obj.strftime('%Y-%m-%d %H:%M')
            except:
                formatted_date = created_at
        else:
            formatted_date = 'غير محدد'
        
        order_type = self.invoice.get('type', '')
        type_text = {'dine-in': 'طاولة', 'takeaway': 'سفري', 'delivery': 'دلفري'}.get(order_type, order_type)
        
        info_text = f"""
        <b>نوع الطلب:</b> {type_text}<br>
        <b>التاريخ:</b> {formatted_date}<br>
        <b>النادل:</b> {self.invoice.get('waiterName', 'غير محدد')}<br>
        <b>الحالة:</b> {self.get_status_text(self.invoice.get('status', ''))}<br>
        """
        
        if order_type == 'dine-in':
            info_text += f"<b>رقم الطاولة:</b> {self.invoice.get('tableNumber', 'غير محدد')}<br>"
        elif order_type == 'delivery':
            info_text += f"<b>رقم الهاتف:</b> {self.invoice.get('customerPhone', 'غير محدد')}<br>"
            info_text += f"<b>العنوان:</b> {self.invoice.get('customerAddress', 'غير محدد')}<br>"
        
        info_label = QLabel(info_text)
        info_label.setFont(QFont("Arial", 12))
        info_label.setStyleSheet("color: #333;")
        info_layout.addWidget(info_label)
        
        layout.addWidget(info_frame)
        
        # Items table
        items_label = QLabel("أصناف الطلب:")
        items_label.setFont(QFont("Arial", 14, QFont.Bold))
        items_label.setStyleSheet("color: #1E2A38;")
        layout.addWidget(items_label)
        
        self.items_table = QTableWidget()
        self.items_table.setColumnCount(4)
        self.items_table.setHorizontalHeaderLabels(["الصنف", "الكمية", "السعر", "المجموع"])
        
        # Set table properties
        header = self.items_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.Stretch)
        
        self.items_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #ddd;
                background-color: white;
                alternate-background-color: #f9f9f9;
                border: 2px solid #ddd;
                border-radius: 8px;
            }
            QTableWidget::item {
                padding: 8px;
            }
            QHeaderView::section {
                background-color: #1E2A38;
                color: white;
                padding: 8px;
                border: none;
                font-weight: bold;
            }
        """)
        
        # Populate items
        items = self.invoice.get('items', [])
        self.items_table.setRowCount(len(items))
        
        for row, item in enumerate(items):
            quantity = item.get('quantity', 0)
            price = item.get('price', 0)
            total = quantity * price
            
            # Format quantity display
            if quantity == 0.5:
                qty_text = "½"
            elif quantity == 0.25:
                qty_text = "¼"
            elif quantity == int(quantity):
                qty_text = str(int(quantity))
            else:
                qty_text = f"{quantity:.2f}"
            
            self.items_table.setItem(row, 0, QTableWidgetItem(item.get('name', '')))
            # Format prices as Iraqi currency
            formatted_price = f"{int(price):,}".replace(',', '.')
            formatted_total = f"{int(total):,}".replace(',', '.')

            self.items_table.setItem(row, 1, QTableWidgetItem(qty_text))
            self.items_table.setItem(row, 2, QTableWidgetItem(f"{formatted_price} دينار"))
            self.items_table.setItem(row, 3, QTableWidgetItem(f"{formatted_total} دينار"))
        
        layout.addWidget(self.items_table)
        
        # Total
        total_frame = QFrame()
        total_frame.setStyleSheet("""
            QFrame {
                background-color: #1E2A38;
                border-radius: 8px;
                padding: 15px;
            }
        """)
        total_layout = QHBoxLayout(total_frame)
        
        # Format total as Iraqi currency
        total_amount = int(self.invoice.get('totalAmount', 0))
        formatted_total = f"{total_amount:,}".replace(',', '.')
        total_label = QLabel(f"المجموع الكلي: {formatted_total} دينار")
        total_label.setFont(QFont("Arial", 16, QFont.Bold))
        total_label.setStyleSheet("color: white;")
        total_label.setAlignment(Qt.AlignCenter)
        total_layout.addWidget(total_label)
        
        layout.addWidget(total_frame)
        
        # Notes
        notes = self.invoice.get('notes', '')
        if notes:
            notes_label = QLabel("ملاحظات:")
            notes_label.setFont(QFont("Arial", 12, QFont.Bold))
            notes_label.setStyleSheet("color: #1E2A38;")
            layout.addWidget(notes_label)
            
            notes_text = QTextEdit()
            notes_text.setPlainText(notes)
            notes_text.setReadOnly(True)
            notes_text.setFixedHeight(60)
            notes_text.setStyleSheet("""
                QTextEdit {
                    border: 2px solid #ddd;
                    border-radius: 5px;
                    padding: 8px;
                    background-color: #f8f9fa;
                }
            """)
            layout.addWidget(notes_text)
        
        # Close button
        close_btn = QPushButton("إغلاق")
        close_btn.setFont(QFont("Arial", 12, QFont.Bold))
        close_btn.setFixedHeight(40)
        close_btn.setStyleSheet("""
            QPushButton {
                background-color: #6c757d;
                color: white;
                border: none;
                border-radius: 8px;
                padding: 10px 20px;
            }
            QPushButton:hover {
                background-color: #545b62;
            }
        """)
        close_btn.clicked.connect(self.accept)
        layout.addWidget(close_btn)
        
        self.setLayout(layout)
    
    def get_status_text(self, status):
        """Get Arabic status text"""
        status_map = {
            'pending': 'معلق',
            'preparing': 'قيد التحضير',
            'ready': 'جاهز',
            'completed': 'مكتمل',
            'cancelled': 'ملغي'
        }
        return status_map.get(status, status)
