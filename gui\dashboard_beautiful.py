#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام إدارة مطعم النوفوس - الشاشة الرئيسية الجميلة
Restaurant Management System - Beautiful Dashboard

تطوير: شركة الباش البرمجية
Developer: Al-Bash Software Company
الهاتف/Phone: 07715086335
واتساب/WhatsApp: 07715086335
البريد الإلكتروني/Email: <EMAIL>
الموقع/Website: www.albash-software.com

© 2025 شركة الباش البرمجية. جميع الحقوق محفوظة.
© 2025 Al-Bash Software Company. All rights reserved.
"""

from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *
import requests

class BeautifulDashboard(QWidget):
    def __init__(self, main_app):
        super().__init__()
        self.main_app = main_app
        self.pending_orders_count = 0
        self.setup_ui()
        self.setup_animations()
        self.setup_timer()
    
    def setup_ui(self):
        """Set up the beautiful dashboard UI"""
        layout = QVBoxLayout()
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)
        
        # Set beautiful background gradient
        self.setStyleSheet("""
            QWidget {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #1E2A38, stop:0.3 #2C3E50, stop:0.7 #34495E, stop:1 #D4AF37);
            }
        """)
        
        # Create header with glass effect
        self.create_beautiful_header(layout)
        
        # Create main content with animations
        self.create_beautiful_main_content(layout)
        
        # Create footer with company info
        self.create_beautiful_footer(layout)
        
        self.setLayout(layout)
    
    def create_beautiful_header(self, parent_layout):
        """Create beautiful header with glass effect"""
        header_frame = QFrame()
        header_frame.setFixedHeight(100)
        header_frame.setStyleSheet("""
            QFrame {
                background: rgba(255, 255, 255, 0.15);
                border-bottom: 3px solid #D4AF37;
                border-radius: 0px;
            }
        """)
        
        # Add shadow effect
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(20)
        shadow.setColor(QColor(0, 0, 0, 60))
        shadow.setOffset(0, 5)
        header_frame.setGraphicsEffect(shadow)
        
        header_layout = QHBoxLayout(header_frame)
        header_layout.setContentsMargins(30, 20, 30, 20)
        header_layout.setSpacing(20)
        
        # Logo and title
        logo_title_layout = QHBoxLayout()
        
        # Logo
        logo_label = QLabel("🍽️")
        logo_label.setFont(QFont("Arial", 36))
        logo_label.setStyleSheet("""
            QLabel {
                color: white;
                text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
            }
        """)
        logo_title_layout.addWidget(logo_label)
        
        # Title and subtitle
        title_container = QVBoxLayout()
        title_container.setSpacing(5)
        
        title_label = QLabel("مطعم النوفوس")
        title_label.setFont(QFont("Arial", 24, QFont.Bold))
        title_label.setStyleSheet("""
            QLabel {
                color: white;
                text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
            }
        """)
        title_container.addWidget(title_label)
        
        subtitle_label = QLabel("نظام إدارة المطعم")
        subtitle_label.setFont(QFont("Arial", 14))
        subtitle_label.setStyleSheet("""
            QLabel {
                color: #D4AF37;
                text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
            }
        """)
        title_container.addWidget(subtitle_label)
        
        logo_title_layout.addLayout(title_container)
        header_layout.addLayout(logo_title_layout)
        
        header_layout.addStretch()
        
        # Time and date
        self.create_time_section(header_layout)
        
        # Action buttons
        self.create_header_buttons(header_layout)
        
        parent_layout.addWidget(header_frame)
    
    def create_time_section(self, parent_layout):
        """Create beautiful time and date section"""
        time_container = QVBoxLayout()
        time_container.setAlignment(Qt.AlignCenter)
        time_container.setSpacing(5)
        
        # Time
        self.time_label = QLabel()
        self.time_label.setFont(QFont("Arial", 18, QFont.Bold))
        self.time_label.setStyleSheet("""
            QLabel {
                color: white;
                text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
            }
        """)
        time_container.addWidget(self.time_label)
        
        # Date
        self.date_label = QLabel()
        self.date_label.setFont(QFont("Arial", 12))
        self.date_label.setStyleSheet("""
            QLabel {
                color: #D4AF37;
                text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
            }
        """)
        time_container.addWidget(self.date_label)
        
        parent_layout.addLayout(time_container)
    
    def create_header_buttons(self, parent_layout):
        """Create beautiful header buttons"""
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(15)

        # Pending orders button with notification
        pending_container = QWidget()
        pending_container.setFixedSize(80, 60)

        self.pending_orders_btn = QPushButton("👨‍🍳")
        self.pending_orders_btn.setParent(pending_container)
        self.pending_orders_btn.setGeometry(0, 0, 60, 60)
        self.pending_orders_btn.setStyleSheet("""
            QPushButton {
                background: rgba(255, 255, 255, 0.2);
                color: #D4AF37;
                border: 3px solid #D4AF37;
                border-radius: 30px;
                font-size: 28px;
            }
            QPushButton:hover {
                background: #D4AF37;
                color: #1E2A38;
            }
        """)
        self.pending_orders_btn.clicked.connect(self.main_app.show_pending_orders)

        # Notification badge
        self.notification_badge = QLabel("0")
        self.notification_badge.setParent(pending_container)
        self.notification_badge.setGeometry(45, 5, 30, 25)
        self.notification_badge.setAlignment(Qt.AlignCenter)
        self.notification_badge.setStyleSheet("""
            QLabel {
                background-color: #e74c3c;
                color: white;
                border-radius: 12px;
                font-size: 14px;
                font-weight: bold;
                padding: 3px;
                min-width: 20px;
            }
        """)
        self.notification_badge.hide()

        buttons_layout.addWidget(pending_container)

        # Invoices button
        invoices_btn = self.create_header_button("🧾", self.main_app.show_invoices)
        buttons_layout.addWidget(invoices_btn)

        # Sales button
        sales_btn = self.create_header_button("📊", self.main_app.show_sales)
        buttons_layout.addWidget(sales_btn)

        # About button
        about_btn = self.create_header_button("ℹ️", self.show_about)
        buttons_layout.addWidget(about_btn)

        # Settings button
        settings_btn = self.create_header_button("⚙️", self.show_settings)
        buttons_layout.addWidget(settings_btn)

        parent_layout.addLayout(buttons_layout)
    
    def create_header_button(self, icon, callback):
        """Create beautiful header button"""
        button = QPushButton(icon)
        button.setFixedSize(60, 60)
        button.setStyleSheet("""
            QPushButton {
                background: rgba(255, 255, 255, 0.2);
                color: #D4AF37;
                border: 3px solid #D4AF37;
                border-radius: 30px;
                font-size: 24px;
            }
            QPushButton:hover {
                background: #D4AF37;
                color: #1E2A38;
                transform: scale(1.1);
            }
        """)
        button.clicked.connect(callback)
        return button
    
    def create_beautiful_main_content(self, parent_layout):
        """Create beautiful main content area"""
        content_frame = QFrame()
        content_frame.setStyleSheet("""
            QFrame {
                background: rgba(255, 255, 255, 0.05);
                border-radius: 0px;
            }
        """)
        
        content_layout = QVBoxLayout(content_frame)
        content_layout.setContentsMargins(50, 50, 50, 50)
        content_layout.setSpacing(40)
        content_layout.setAlignment(Qt.AlignCenter)
        
        # Welcome message
        welcome_label = QLabel("مرحباً بك في نظام إدارة المطعم")
        welcome_label.setAlignment(Qt.AlignCenter)
        welcome_label.setFont(QFont("Arial", 20, QFont.Bold))
        welcome_label.setStyleSheet("""
            QLabel {
                color: white;
                text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
                margin-bottom: 30px;
            }
        """)
        content_layout.addWidget(welcome_label)
        
        # Main buttons grid
        self.create_main_buttons_grid(content_layout)
        
        parent_layout.addWidget(content_frame)
    
    def create_main_buttons_grid(self, parent_layout):
        """Create beautiful main buttons grid - 3 main buttons only"""
        grid_layout = QHBoxLayout()
        grid_layout.setSpacing(40)
        grid_layout.setAlignment(Qt.AlignCenter)

        # Main 3 buttons data: (text, icon, callback, color)
        buttons_data = [
            ("الطاولات", "🪑", self.main_app.show_tables, "#2C3E50"),
            ("سفري", "🥡", self.main_app.show_takeaway, "#27AE60"),
            ("دلفري", "🚗", self.main_app.show_delivery, "#E74C3C"),
        ]

        for text, icon, callback, bg_color in buttons_data:
            button = self.create_main_button(text, icon, callback, bg_color)
            grid_layout.addWidget(button)

        parent_layout.addLayout(grid_layout)
    
    def create_main_button(self, text, icon, callback, bg_color="#2C3E50"):
        """Create beautiful main button with custom background color"""
        button = QPushButton()
        button.clicked.connect(callback)

        # Get screen size for responsive design
        screen = QApplication.primaryScreen()
        screen_width = screen.size().width()

        # Calculate responsive sizes
        if screen_width < 800:
            button_width = 220
            button_height = 180
            font_size = 16
        elif screen_width < 1200:
            button_width = 280
            button_height = 220
            font_size = 18
        else:
            button_width = 350
            button_height = 280
            font_size = 22

        button.setFixedSize(button_width, button_height)
        button.setText(f"{icon}\n{text}")
        button.setFont(QFont("Arial", font_size, QFont.Bold))

        # Convert hex color to rgba for hover effect
        color = QColor(bg_color)
        hover_color = f"rgba({color.red()}, {color.green()}, {color.blue()}, 0.8)"
        pressed_color = f"rgba({color.red()}, {color.green()}, {color.blue()}, 0.9)"

        button.setStyleSheet(f"""
            QPushButton {{
                background: {bg_color};
                color: white;
                border: 3px solid rgba(255, 255, 255, 0.3);
                border-radius: 25px;
                font-size: {font_size}px;
                font-weight: bold;
                padding: 25px;
                text-align: center;
                line-height: 1.4;
            }}
            QPushButton:hover {{
                background: {hover_color};
                border: 3px solid #D4AF37;
                color: white;
            }}
            QPushButton:pressed {{
                background: {pressed_color};
            }}
        """)

        # Add shadow effect
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(25)
        shadow.setColor(QColor(0, 0, 0, 100))
        shadow.setOffset(0, 12)
        button.setGraphicsEffect(shadow)

        return button
    
    def create_beautiful_footer(self, parent_layout):
        """Create beautiful footer"""
        footer_frame = QFrame()
        footer_frame.setFixedHeight(60)
        footer_frame.setStyleSheet("""
            QFrame {
                background: rgba(255, 255, 255, 0.1);
                border-top: 2px solid rgba(255, 255, 255, 0.2);
            }
        """)
        
        footer_layout = QHBoxLayout(footer_frame)
        footer_layout.setContentsMargins(30, 15, 30, 15)
        
        # Company info
        company_label = QLabel("شركة الباش البرمجية")
        company_label.setFont(QFont("Arial", 14, QFont.Bold))
        company_label.setStyleSheet("""
            QLabel {
                color: #D4AF37;
                text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
            }
        """)
        footer_layout.addWidget(company_label)
        
        footer_layout.addStretch()
        
        # Contact info
        contact_label = QLabel("للدعم: 07715086335")
        contact_label.setFont(QFont("Arial", 12))
        contact_label.setStyleSheet("""
            QLabel {
                color: white;
                text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
            }
        """)
        footer_layout.addWidget(contact_label)
        
        footer_layout.addStretch()
        
        # Copyright
        copyright_label = QLabel("© 2025 جميع الحقوق محفوظة")
        copyright_label.setFont(QFont("Arial", 10))
        copyright_label.setStyleSheet("""
            QLabel {
                color: rgba(255, 255, 255, 0.7);
                text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
            }
        """)
        footer_layout.addWidget(copyright_label)
        
        parent_layout.addWidget(footer_frame)
    
    def setup_animations(self):
        """Setup beautiful animations"""
        # Fade in animation
        self.fade_animation = QPropertyAnimation(self, b"windowOpacity")
        self.fade_animation.setDuration(1000)
        self.fade_animation.setStartValue(0.0)
        self.fade_animation.setEndValue(1.0)
        self.fade_animation.setEasingCurve(QEasingCurve.OutCubic)
    
    def setup_timer(self):
        """Setup timer for updates"""
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_time)
        self.timer.timeout.connect(self.update_pending_orders)
        self.timer.start(1000)  # Update every second
        
        # Initial update
        self.update_time()
        self.update_pending_orders()
    
    def update_time(self):
        """Update time and date display"""
        current_time = QDateTime.currentDateTime()
        time_text = current_time.toString("hh:mm:ss")
        date_text = current_time.toString("yyyy/MM/dd")
        
        self.time_label.setText(time_text)
        self.date_label.setText(date_text)
    
    def update_pending_orders(self):
        """Update pending orders count"""
        try:
            response = requests.get("http://localhost:3001/api/waiter-orders/pending", timeout=2)
            if response.status_code == 200:
                data = response.json()
                count = len(data.get('orders', []))
                self.update_pending_orders_count(count)
        except:
            pass  # Ignore connection errors
    
    def update_pending_orders_count(self, count):
        """Update the pending orders notification badge"""
        if count > 0:
            self.notification_badge.setText(str(count))
            self.notification_badge.show()
        else:
            self.notification_badge.hide()
    
    def show_about(self):
        """Show about dialog"""
        from gui.about import AboutDialog
        about_dialog = AboutDialog(self)
        about_dialog.exec_()
    
    def show_settings(self):
        """Show settings dialog"""
        from gui.settings import SettingsDialog
        settings_dialog = SettingsDialog(self.main_app)
        settings_dialog.exec_()
    
    def showEvent(self, event):
        """Override show event to start animations"""
        super().showEvent(event)
        self.fade_animation.start()
