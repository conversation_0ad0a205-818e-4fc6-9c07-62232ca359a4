from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                             QPushButton, QFrame, QGridLayout, QScrollArea,
                             QMessageBox, QInputDialog, QComboBox)
from PyQt5.QtCore import Qt, QThread, pyqtSignal
from PyQt5.QtGui import QFont

class DataLoadThread(QThread):
    """Thread for loading halls and tables data"""
    data_loaded = pyqtSignal(dict)
    
    def __init__(self, api_client):
        super().__init__()
        self.api_client = api_client
    
    def run(self):
        result = self.api_client.get_halls()
        self.data_loaded.emit(result)

class TablesWindow(QWidget):
    def __init__(self, main_app):
        super().__init__()
        self.main_app = main_app
        self.current_hall = None
        self.halls_data = []
        self.setup_ui()
    
    def setup_ui(self):
        """Set up the tables UI"""
        layout = QVBoxLayout()
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)
        
        # Top bar (same as dashboard)
        self.create_top_bar(layout)
        
        # Halls selection bar
        self.create_halls_bar(layout)
        
        # Tables grid area
        self.create_tables_area(layout)
        
        # Footer navigation
        self.create_footer_navigation(layout)
        
        self.setLayout(layout)
    
    def create_top_bar(self, parent_layout):
        """Create top navigation bar"""
        top_bar = QFrame()
        top_bar.setFixedHeight(80)
        top_bar.setStyleSheet("""
            QFrame {
                background-color: #1E2A38;
                border-bottom: 3px solid #D4AF37;
            }
        """)
        
        top_layout = QHBoxLayout(top_bar)
        top_layout.setContentsMargins(20, 10, 20, 10)
        
        # Notifications icon (left)
        notifications_btn = QPushButton("🔔")
        notifications_btn.setFixedSize(50, 50)
        notifications_btn.setStyleSheet("""
            QPushButton {
                background-color: transparent;
                color: #D4AF37;
                border: 2px solid #D4AF37;
                border-radius: 25px;
                font-size: 20px;
            }
            QPushButton:hover {
                background-color: #D4AF37;
                color: #1E2A38;
            }
        """)
        top_layout.addWidget(notifications_btn)
        
        # Center area - Page title
        center_widget = QWidget()
        center_layout = QVBoxLayout(center_widget)
        center_layout.setAlignment(Qt.AlignCenter)
        center_layout.setSpacing(5)
        
        page_title = QLabel("إدارة الطاولات")
        page_title.setAlignment(Qt.AlignCenter)
        page_title.setFont(QFont("Arial", 18, QFont.Bold))
        page_title.setStyleSheet("color: white;")
        center_layout.addWidget(page_title)
        
        top_layout.addWidget(center_widget)
        
        # Settings icon (right)
        settings_btn = QPushButton("⚙️")
        settings_btn.setFixedSize(50, 50)
        settings_btn.setStyleSheet("""
            QPushButton {
                background-color: transparent;
                color: #D4AF37;
                border: 2px solid #D4AF37;
                border-radius: 25px;
                font-size: 20px;
            }
            QPushButton:hover {
                background-color: #D4AF37;
                color: #1E2A38;
            }
        """)
        settings_btn.clicked.connect(self.show_settings)
        top_layout.addWidget(settings_btn)
        
        parent_layout.addWidget(top_bar)
    
    def create_halls_bar(self, parent_layout):
        """Create halls selection bar"""
        halls_bar = QFrame()
        halls_bar.setFixedHeight(70)
        halls_bar.setStyleSheet("""
            QFrame {
                background-color: white;
                border-bottom: 2px solid #ddd;
            }
        """)
        
        halls_layout = QHBoxLayout(halls_bar)
        halls_layout.setContentsMargins(20, 10, 20, 10)
        halls_layout.setSpacing(15)
        
        # Halls label
        halls_label = QLabel("الصالات:")
        halls_label.setFont(QFont("Arial", 14, QFont.Bold))
        halls_label.setStyleSheet("color: #1E2A38;")
        halls_layout.addWidget(halls_label)
        
        # Halls buttons container
        self.halls_container = QWidget()
        self.halls_buttons_layout = QHBoxLayout(self.halls_container)
        self.halls_buttons_layout.setSpacing(10)
        self.halls_buttons_layout.setContentsMargins(0, 0, 0, 0)
        
        halls_layout.addWidget(self.halls_container)
        
        # Add hall button
        add_hall_btn = QPushButton("➕ إضافة صالة")
        add_hall_btn.setFont(QFont("Arial", 12, QFont.Bold))
        add_hall_btn.setStyleSheet("""
            QPushButton {
                background-color: #D4AF37;
                color: white;
                border: none;
                border-radius: 8px;
                padding: 8px 16px;
            }
            QPushButton:hover {
                background-color: #B8941F;
            }
        """)
        add_hall_btn.clicked.connect(self.add_hall)
        halls_layout.addWidget(add_hall_btn)
        
        halls_layout.addStretch()
        
        parent_layout.addWidget(halls_bar)
    
    def create_tables_area(self, parent_layout):
        """Create tables grid area"""
        # Scroll area for tables
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setStyleSheet("""
            QScrollArea {
                border: none;
                background-color: #F5F5F5;
            }
        """)
        
        # Tables container
        self.tables_container = QWidget()
        self.tables_layout = QGridLayout(self.tables_container)
        self.tables_layout.setSpacing(20)
        self.tables_layout.setContentsMargins(30, 30, 30, 30)
        
        scroll_area.setWidget(self.tables_container)
        parent_layout.addWidget(scroll_area)
    
    def create_footer_navigation(self, parent_layout):
        """Create footer navigation bar"""
        footer = QFrame()
        footer.setFixedHeight(70)
        footer.setStyleSheet("""
            QFrame {
                background-color: #1E2A38;
                border-top: 2px solid #D4AF37;
            }
        """)
        
        footer_layout = QHBoxLayout(footer)
        footer_layout.setContentsMargins(20, 10, 20, 10)
        footer_layout.setSpacing(20)
        
        # Back button
        back_btn = QPushButton("🔙 العودة")
        back_btn.setFont(QFont("Arial", 12, QFont.Bold))
        back_btn.setStyleSheet("""
            QPushButton {
                background-color: transparent;
                color: #D4AF37;
                border: 2px solid #D4AF37;
                border-radius: 8px;
                padding: 8px 16px;
            }
            QPushButton:hover {
                background-color: #D4AF37;
                color: #1E2A38;
            }
        """)
        back_btn.clicked.connect(self.main_app.show_dashboard)
        footer_layout.addWidget(back_btn)
        
        # Spacer
        footer_layout.addStretch()
        
        # Old invoices button
        invoices_btn = QPushButton("📜 عرض الفواتير القديمة")
        invoices_btn.setFont(QFont("Arial", 12, QFont.Bold))
        invoices_btn.setStyleSheet("""
            QPushButton {
                background-color: transparent;
                color: #D4AF37;
                border: 2px solid #D4AF37;
                border-radius: 8px;
                padding: 8px 16px;
            }
            QPushButton:hover {
                background-color: #D4AF37;
                color: #1E2A38;
            }
        """)
        invoices_btn.clicked.connect(self.main_app.show_invoices)
        footer_layout.addWidget(invoices_btn)
        
        # Sales summary button
        sales_btn = QPushButton("📊 مبيعات اليوم")
        sales_btn.setFont(QFont("Arial", 12, QFont.Bold))
        sales_btn.setStyleSheet("""
            QPushButton {
                background-color: transparent;
                color: #D4AF37;
                border: 2px solid #D4AF37;
                border-radius: 8px;
                padding: 8px 16px;
            }
            QPushButton:hover {
                background-color: #D4AF37;
                color: #1E2A38;
            }
        """)
        sales_btn.clicked.connect(self.main_app.show_sales)
        footer_layout.addWidget(sales_btn)
        
        parent_layout.addWidget(footer)

    def refresh_data(self):
        """Refresh halls and tables data"""
        self.load_thread = DataLoadThread(self.main_app.api_client)
        self.load_thread.data_loaded.connect(self.handle_data_loaded)
        self.load_thread.start()

    def handle_data_loaded(self, result):
        """Handle loaded halls data"""
        if 'error' in result:
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل البيانات: {result['error']}")
            return

        if 'halls' in result:
            self.halls_data = result['halls']
            self.update_halls_buttons()

            # Select first hall by default
            if self.halls_data and not self.current_hall:
                self.select_hall(self.halls_data[0])

    def update_halls_buttons(self):
        """Update halls selection buttons"""
        # Clear existing buttons
        for i in reversed(range(self.halls_buttons_layout.count())):
            child = self.halls_buttons_layout.itemAt(i).widget()
            if child:
                child.setParent(None)

        # Add hall buttons
        for hall in self.halls_data:
            hall_btn = QPushButton(hall['name'])
            hall_btn.setFont(QFont("Arial", 12, QFont.Bold))
            hall_btn.clicked.connect(lambda checked, h=hall: self.select_hall(h))

            # Style based on selection
            if self.current_hall and self.current_hall['id'] == hall['id']:
                hall_btn.setStyleSheet("""
                    QPushButton {
                        background-color: #1E2A38;
                        color: white;
                        border: none;
                        border-radius: 8px;
                        padding: 8px 16px;
                    }
                """)
            else:
                hall_btn.setStyleSheet("""
                    QPushButton {
                        background-color: white;
                        color: #1E2A38;
                        border: 2px solid #1E2A38;
                        border-radius: 8px;
                        padding: 8px 16px;
                    }
                    QPushButton:hover {
                        background-color: #1E2A38;
                        color: white;
                    }
                """)

            self.halls_buttons_layout.addWidget(hall_btn)

    def select_hall(self, hall):
        """Select a hall and display its tables"""
        self.current_hall = hall
        self.update_halls_buttons()
        self.update_tables_grid()

    def update_tables_grid(self):
        """Update tables grid display"""
        # Clear existing tables
        for i in reversed(range(self.tables_layout.count())):
            child = self.tables_layout.itemAt(i).widget()
            if child:
                child.setParent(None)

        if not self.current_hall:
            return

        # Add table buttons
        tables = self.current_hall.get('tables', [])
        columns = 4  # Number of columns in grid

        for i, table in enumerate(tables):
            row = i // columns
            col = i % columns

            table_btn = self.create_table_button(table)
            self.tables_layout.addWidget(table_btn, row, col)

    def create_table_button(self, table):
        """Create a table button"""
        table_btn = QPushButton()
        table_btn.setFixedSize(150, 120)
        table_btn.clicked.connect(lambda: self.select_table(table))

        # Set button text and style based on table status
        table_text = f"طاولة {table['number']}"

        if table['status'] == 'available':
            table_btn.setText(f"🍽️\n{table_text}\nمتاحة")
            table_btn.setStyleSheet("""
                QPushButton {
                    background-color: #28a745;
                    color: white;
                    border: none;
                    border-radius: 10px;
                    font-size: 12px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #218838;
                }
            """)
        elif table['status'] == 'occupied':
            table_btn.setText(f"🔴\n{table_text}\nمشغولة")
            table_btn.setStyleSheet("""
                QPushButton {
                    background-color: #dc3545;
                    color: white;
                    border: none;
                    border-radius: 10px;
                    font-size: 12px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #c82333;
                }
            """)
        else:  # reserved
            table_btn.setText(f"🟡\n{table_text}\nمحجوزة")
            table_btn.setStyleSheet("""
                QPushButton {
                    background-color: #ffc107;
                    color: #212529;
                    border: none;
                    border-radius: 10px;
                    font-size: 12px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #e0a800;
                }
            """)

        return table_btn

    def select_table(self, table):
        """Handle table selection"""
        # Open menu for this table
        self.main_app.show_menu('dine-in', self.current_hall['id'], table['number'])

    def add_hall(self):
        """Add new hall"""
        hall_name, ok = QInputDialog.getText(self, 'إضافة صالة جديدة', 'اسم الصالة:')
        if not ok or not hall_name.strip():
            return

        table_count, ok = QInputDialog.getInt(self, 'إضافة صالة جديدة', 'عدد الطاولات:', 4, 1, 50)
        if not ok:
            return

        # Create hall via API
        result = self.main_app.api_client.create_hall(hall_name.strip(), table_count)

        if 'error' in result:
            QMessageBox.critical(self, "خطأ", f"فشل في إضافة الصالة: {result['error']}")
        else:
            QMessageBox.information(self, "نجح", "تم إضافة الصالة بنجاح")
            self.refresh_data()

    def show_settings(self):
        """Show settings dialog"""
        from gui.settings import SettingsDialog
        settings_dialog = SettingsDialog(self.main_app)
        settings_dialog.exec_()

    def load_waiter_order(self, order):
        """Load waiter order for editing"""
        try:
            # Set hall and table based on order
            if order.get('hallId') and order.get('tableNumber'):
                self.current_hall = order.get('hallId')
                table_number = order.get('tableNumber')

                # Find and select the hall
                for i, hall in enumerate(self.halls_data):
                    if hall.get('id') == self.current_hall:
                        # Update hall selection
                        self.update_hall_display()
                        break

                # Open order window for the specific table
                self.open_table_order(table_number, order)
            else:
                QMessageBox.critical(self, "خطأ", "معلومات الطاولة غير مكتملة في الطلب")

        except Exception as e:
            print(f"Error loading waiter order: {e}")
            QMessageBox.critical(self, "خطأ", f"خطأ في تحميل طلب النادل: {str(e)}")

    def open_table_order(self, table_number, waiter_order=None):
        """Open order window for specific table with optional waiter order data"""
        try:
            from gui.order import OrderWindow

            # Create order window
            order_window = OrderWindow(
                self.main_app,
                'dine-in',
                hall_id=self.current_hall,
                table_number=table_number
            )

            # If waiter order data is provided, load it
            if waiter_order:
                order_window.load_waiter_order_data(waiter_order)

            order_window.show()

        except Exception as e:
            print(f"Error opening table order: {e}")
            QMessageBox.critical(self, "خطأ", f"خطأ في فتح طلب الطاولة: {str(e)}")
