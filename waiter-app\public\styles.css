/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', Arial, sans-serif;
    background: linear-gradient(135deg, #1E2A38 0%, #D4AF37 100%);
    min-height: 100vh;
    color: #333;
    direction: rtl;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header */
.header {
    background: rgba(30, 42, 56, 0.95);
    backdrop-filter: blur(10px);
    color: white;
    padding: 15px 0;
    position: sticky;
    top: 0;
    z-index: 100;
    box-shadow: 0 2px 20px rgba(0,0,0,0.1);
}

.header .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.header h1 {
    font-size: 24px;
    font-weight: 700;
}

.waiter-info {
    display: flex;
    align-items: center;
    gap: 15px;
}

.notifications {
    position: relative;
    cursor: pointer;
    padding: 8px;
    border-radius: 50%;
    background: rgba(212, 175, 55, 0.2);
    transition: all 0.3s ease;
}

.notifications:hover {
    background: rgba(212, 175, 55, 0.4);
}

.notification-icon {
    font-size: 20px;
}

.notification-count {
    position: absolute;
    top: -5px;
    right: -5px;
    background: #ff4757;
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: bold;
}

/* Main Content */
.main-content {
    padding: 30px 0;
}

/* Order Type Section */
.order-type-section {
    background: white;
    border-radius: 15px;
    padding: 30px;
    margin-bottom: 30px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    text-align: center;
}

.order-type-section h2 {
    margin-bottom: 30px;
    color: #1E2A38;
    font-size: 28px;
    font-weight: 600;
}

.order-type-buttons {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    max-width: 800px;
    margin: 0 auto;
}

.order-type-btn {
    background: linear-gradient(135deg, #1E2A38, #2c3e50);
    color: white;
    border: none;
    border-radius: 15px;
    padding: 30px 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 15px;
    font-size: 18px;
    font-weight: 600;
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

.order-type-btn:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(0,0,0,0.3);
    background: linear-gradient(135deg, #D4AF37, #f39c12);
}

.order-type-btn .icon {
    font-size: 40px;
}

/* Table Selection */
.table-selection {
    background: white;
    border-radius: 15px;
    padding: 30px;
    margin-bottom: 30px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.table-selection h2 {
    margin-bottom: 30px;
    color: #1E2A38;
    font-size: 24px;
    text-align: center;
}

.halls-container {
    display: grid;
    gap: 30px;
}

.hall {
    border: 2px solid #e9ecef;
    border-radius: 10px;
    padding: 20px;
}

.hall h3 {
    margin-bottom: 15px;
    color: #1E2A38;
    font-size: 20px;
}

.tables-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
    gap: 10px;
}

.table-btn {
    background: #f8f9fa;
    border: 2px solid #dee2e6;
    border-radius: 8px;
    padding: 15px 10px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 600;
    color: #1E2A38;
}

.table-btn:hover {
    background: #D4AF37;
    color: white;
    border-color: #D4AF37;
}

.table-btn.occupied {
    background: #ff6b6b;
    color: white;
    border-color: #ff6b6b;
    cursor: not-allowed;
}

/* Delivery Info */
.delivery-info {
    background: white;
    border-radius: 15px;
    padding: 30px;
    margin-bottom: 30px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.delivery-info h2 {
    margin-bottom: 30px;
    color: #1E2A38;
    font-size: 24px;
    text-align: center;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #1E2A38;
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 12px;
    border: 2px solid #dee2e6;
    border-radius: 8px;
    font-size: 16px;
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #D4AF37;
}

.form-group textarea {
    resize: vertical;
    min-height: 80px;
}

/* Menu Section */
.menu-section {
    background: white;
    border-radius: 15px;
    padding: 30px;
    margin-bottom: 30px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.menu-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 2px solid #e9ecef;
}

.menu-header h2 {
    color: #1E2A38;
    font-size: 24px;
}

.order-info {
    background: #D4AF37;
    color: white;
    padding: 10px 20px;
    border-radius: 25px;
    font-weight: 600;
}

.menu-content {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 30px;
}

.menu-categories,
.menu-items,
.order-summary {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 20px;
}

.menu-categories h3,
.menu-items h3,
.order-summary h3 {
    margin-bottom: 20px;
    color: #1E2A38;
    font-size: 18px;
    text-align: center;
}

.categories-list,
.items-list {
    display: flex;
    flex-direction: column;
    gap: 10px;
    max-height: 400px;
    overflow-y: auto;
}

.category-item,
.menu-item {
    background: white;
    border: 2px solid #dee2e6;
    border-radius: 8px;
    padding: 15px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.category-item:hover,
.menu-item:hover {
    border-color: #D4AF37;
    background: #fff8e1;
}

.category-item.active {
    background: #D4AF37;
    color: white;
    border-color: #D4AF37;
}

.menu-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.item-name {
    font-weight: 600;
}

.item-price {
    color: #28a745;
    font-weight: bold;
}

/* Order Summary */
.order-items {
    max-height: 300px;
    overflow-y: auto;
    margin-bottom: 20px;
}

.order-item {
    background: white;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 10px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border: 1px solid #dee2e6;
}

.order-item-info {
    flex: 1;
}

.order-item-name {
    font-weight: 600;
    margin-bottom: 5px;
}

.order-item-details {
    color: #666;
    font-size: 14px;
}

.remove-item-btn {
    background: #ff4757;
    color: white;
    border: none;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.remove-item-btn:hover {
    background: #ff3838;
    transform: scale(1.1);
}

.order-total {
    background: #1E2A38;
    color: white;
    padding: 15px;
    border-radius: 8px;
    text-align: center;
    margin-bottom: 20px;
    font-size: 18px;
}

.order-notes textarea {
    width: 100%;
    padding: 10px;
    border: 2px solid #dee2e6;
    border-radius: 8px;
    resize: vertical;
    min-height: 60px;
    margin-top: 10px;
}

/* Buttons */
.continue-btn,
.send-order-btn,
.confirm-btn {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
    border: none;
    border-radius: 8px;
    padding: 12px 24px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    width: 100%;
    margin-bottom: 10px;
}

.continue-btn:hover,
.send-order-btn:hover,
.confirm-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(40, 167, 69, 0.4);
}

.back-btn,
.clear-order-btn,
.cancel-btn {
    background: #6c757d;
    color: white;
    border: none;
    border-radius: 8px;
    padding: 12px 24px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    width: 100%;
    margin-bottom: 10px;
}

.back-btn:hover,
.clear-order-btn:hover,
.cancel-btn:hover {
    background: #545b62;
}

.refresh-btn {
    background: #17a2b8;
    color: white;
    border: none;
    border-radius: 8px;
    padding: 12px 24px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    width: 100%;
}

.refresh-btn:hover {
    background: #138496;
}

/* Pending Orders */
.pending-orders {
    background: white;
    border-radius: 15px;
    padding: 30px;
    margin-bottom: 30px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.pending-orders h2 {
    margin-bottom: 30px;
    color: #1E2A38;
    font-size: 24px;
    text-align: center;
}

.pending-list {
    display: grid;
    gap: 15px;
    margin-bottom: 20px;
}

.pending-item {
    background: #fff3cd;
    border: 2px solid #ffeaa7;
    border-radius: 10px;
    padding: 20px;
}

.pending-item.approved {
    background: #d4edda;
    border-color: #c3e6cb;
}

.pending-item.rejected {
    background: #f8d7da;
    border-color: #f5c6cb;
}

.pending-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.pending-number {
    font-size: 18px;
    font-weight: bold;
    color: #1E2A38;
}

.pending-status {
    padding: 5px 15px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: bold;
    text-transform: uppercase;
}

.pending-status.pending {
    background: #ffc107;
    color: #856404;
}

.pending-status.approved {
    background: #28a745;
    color: white;
}

.pending-status.rejected {
    background: #dc3545;
    color: white;
}

/* Modal */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    backdrop-filter: blur(5px);
}

.modal-content {
    background: white;
    margin: 15% auto;
    padding: 30px;
    border-radius: 15px;
    width: 90%;
    max-width: 400px;
    text-align: center;
    box-shadow: 0 20px 40px rgba(0,0,0,0.3);
}

.modal-content h3 {
    margin-bottom: 30px;
    color: #1E2A38;
    font-size: 20px;
}

.quantity-selector {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 10px;
    margin-bottom: 30px;
}

.quantity-btn {
    background: #f8f9fa;
    border: 2px solid #dee2e6;
    border-radius: 8px;
    padding: 15px;
    cursor: pointer;
    font-size: 16px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.quantity-btn:hover,
.quantity-btn.selected {
    background: #D4AF37;
    color: white;
    border-color: #D4AF37;
}

/* Toast Notifications */
.toast {
    position: fixed;
    bottom: 30px;
    right: 30px;
    background: #1E2A38;
    color: white;
    padding: 15px 25px;
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.3);
    transform: translateX(400px);
    transition: transform 0.3s ease;
    z-index: 1001;
}

.toast.show {
    transform: translateX(0);
}

.toast.success {
    background: #28a745;
}

.toast.error {
    background: #dc3545;
}

.toast.warning {
    background: #ffc107;
    color: #856404;
}

/* Responsive Design */
@media (max-width: 768px) {
    .menu-content {
        grid-template-columns: 1fr;
    }
    
    .order-type-buttons {
        grid-template-columns: 1fr;
    }
    
    .tables-grid {
        grid-template-columns: repeat(auto-fill, minmax(60px, 1fr));
    }
    
    .container {
        padding: 0 15px;
    }
    
    .header h1 {
        font-size: 20px;
    }
}

/* Order actions */
.order-actions {
    margin-top: 20px;
    padding: 15px;
    background-color: #f8f9fa;
    border-radius: 8px;
    border: 2px solid #e9ecef;
}

.action-buttons {
    display: flex;
    gap: 8px;
    margin-bottom: 15px;
    flex-wrap: wrap;
}

.action-buttons button {
    flex: 1;
    min-width: 110px;
    padding: 10px 14px;
    border: none;
    border-radius: 4px;
    font-size: 13px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    margin: 1px;
}

.edit-btn {
    background-color: #ffc107;
    color: #212529;
}

.edit-btn:hover {
    background-color: #e0a800;
}

.cancel-btn {
    background-color: #dc3545;
    color: white;
}

.cancel-btn:hover {
    background-color: #c82333;
}

.new-btn {
    background-color: #28a745;
    color: white;
}

.new-btn:hover {
    background-color: #218838;
}

.order-status {
    text-align: center;
    margin: 0;
    color: #6c757d;
    font-style: italic;
    font-size: 14px;
}

/* Bottom Navigation */
.bottom-nav {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: #1E2A38;
    display: flex;
    justify-content: space-around;
    padding: 10px 0;
    border-top: 2px solid #D4AF37;
    z-index: 1000;
}

.nav-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 8px 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    border-radius: 8px;
    min-width: 60px;
}

.nav-item:hover {
    background-color: rgba(212, 175, 55, 0.1);
}

.nav-item.active {
    background-color: #D4AF37;
    color: #1E2A38;
}

.nav-item.active .nav-icon,
.nav-item.active .nav-text {
    color: #1E2A38;
}

.nav-icon {
    font-size: 20px;
    margin-bottom: 4px;
    color: #D4AF37;
}

.nav-text {
    font-size: 10px;
    font-weight: bold;
    color: #D4AF37;
}

.notification-badge {
    position: absolute;
    top: 2px;
    right: 8px;
    background-color: #dc3545;
    color: white;
    border-radius: 10px;
    padding: 2px 6px;
    font-size: 10px;
    font-weight: bold;
    min-width: 16px;
    text-align: center;
}

/* Order History */
.order-history {
    padding: 20px;
    padding-bottom: 100px; /* Space for bottom nav */
}

.history-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
    margin-bottom: 20px;
}

.history-item {
    background-color: white;
    border-radius: 12px;
    padding: 15px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    border-left: 4px solid #6c757d;
}

.history-item.approved {
    border-left-color: #28a745;
}

.history-item.rejected {
    border-left-color: #dc3545;
}

.history-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.history-number {
    font-weight: bold;
    color: #1E2A38;
    font-size: 16px;
}

.history-status {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: bold;
}

.history-status.approved {
    background-color: #d4edda;
    color: #155724;
}

.history-status.rejected {
    background-color: #f8d7da;
    color: #721c24;
}

.history-actions {
    margin-top: 12px;
    display: flex;
    gap: 6px;
}

.history-actions button {
    flex: 1;
    padding: 6px 10px;
    border: none;
    border-radius: 4px;
    font-size: 11px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    margin: 1px;
}

.resend-btn {
    background-color: #ffc107;
    color: #212529;
}

.resend-btn:hover {
    background-color: #e0a800;
}

.resend-btn:disabled {
    background-color: #6c757d;
    color: white;
    cursor: not-allowed;
}

.edit-invoice-btn {
    background-color: #17a2b8;
    color: white;
}

.edit-invoice-btn:hover {
    background-color: #138496;
}

.edit-invoice-btn:disabled {
    background-color: #6c757d;
    color: white;
    cursor: not-allowed;
}

/* Adjust main content for bottom nav */
.main-content {
    padding-bottom: 80px;
}

.pending-orders {
    padding-bottom: 100px;
}
