from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *
import requests
from datetime import datetime

class WaiterInvoiceViewer(QWidget):
    def __init__(self, main_app, order_data, approval_mode=False):
        super().__init__()
        self.main_app = main_app
        self.order_data = order_data
        self.approval_mode = approval_mode
        self.init_ui()
    
    def init_ui(self):
        """Initialize the user interface"""
        # Main layout
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)
        
        # Header
        self.create_header(main_layout)
        
        # Invoice content
        self.create_invoice_content(main_layout)
        
        # Footer with action buttons
        self.create_footer(main_layout)
    
    def create_header(self, parent_layout):
        """Create header section"""
        header_frame = QFrame()
        header_frame.setStyleSheet("""
            QFrame {
                background-color: #1E2A38;
                border-radius: 10px;
                padding: 15px;
            }
        """)
        
        header_layout = QHBoxLayout(header_frame)
        
        # Title
        if self.approval_mode:
            title_text = "مراجعة طلب النادل"
        else:
            title_text = "عرض الفاتورة"
            
        title_label = QLabel(title_text)
        title_label.setFont(QFont("Arial", 20, QFont.Bold))
        title_label.setStyleSheet("color: #D4AF37;")
        header_layout.addWidget(title_label)
        
        header_layout.addStretch()
        
        # Order info
        order_info = f"طلب #{self.order_data.get('orderNumber', 'N/A')}"
        if self.order_data.get('isInvoiceEdit'):
            order_info += f" - تعديل فاتورة #{self.order_data.get('originalOrderNumber', 'N/A')}"
        
        info_label = QLabel(order_info)
        info_label.setFont(QFont("Arial", 14, QFont.Bold))
        info_label.setStyleSheet("color: white;")
        header_layout.addWidget(info_label)
        
        parent_layout.addWidget(header_frame)
    
    def create_invoice_content(self, parent_layout):
        """Create invoice content area"""
        content_frame = QFrame()
        content_frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border-radius: 10px;
                padding: 20px;
                border: 2px solid #e9ecef;
            }
        """)
        
        content_layout = QVBoxLayout(content_frame)
        content_layout.setSpacing(15)
        
        # Restaurant header
        restaurant_header = QLabel("مطعم النوفوس")
        restaurant_header.setAlignment(Qt.AlignCenter)
        restaurant_header.setFont(QFont("Arial", 18, QFont.Bold))
        restaurant_header.setStyleSheet("color: #1E2A38; margin-bottom: 10px;")
        content_layout.addWidget(restaurant_header)
        
        # Order details
        details_layout = QGridLayout()
        
        # Waiter name
        details_layout.addWidget(QLabel("النادل:"), 0, 0)
        waiter_label = QLabel(self.order_data.get('waiterName', 'غير محدد'))
        waiter_label.setStyleSheet("font-weight: bold;")
        details_layout.addWidget(waiter_label, 0, 1)
        
        # Order type
        details_layout.addWidget(QLabel("النوع:"), 1, 0)
        type_label = QLabel(self.get_order_type_text(self.order_data.get('type', '')))
        type_label.setStyleSheet("font-weight: bold;")
        details_layout.addWidget(type_label, 1, 1)
        
        # Additional info based on type
        if self.order_data.get('type') == 'dine-in':
            details_layout.addWidget(QLabel("الطاولة:"), 2, 0)
            table_label = QLabel(f"طاولة {self.order_data.get('tableNumber', 'غير محدد')}")
            table_label.setStyleSheet("font-weight: bold;")
            details_layout.addWidget(table_label, 2, 1)
        elif self.order_data.get('type') == 'delivery':
            details_layout.addWidget(QLabel("الهاتف:"), 2, 0)
            phone_label = QLabel(self.order_data.get('customerPhone', 'غير محدد'))
            phone_label.setStyleSheet("font-weight: bold;")
            details_layout.addWidget(phone_label, 2, 1)
            
            details_layout.addWidget(QLabel("العنوان:"), 3, 0)
            address_label = QLabel(self.order_data.get('customerAddress', 'غير محدد'))
            address_label.setStyleSheet("font-weight: bold;")
            details_layout.addWidget(address_label, 3, 1)
        
        # Time
        details_layout.addWidget(QLabel("الوقت:"), 4, 0)
        created_at = self.order_data.get('createdAt', '')
        if created_at:
            try:
                dt = datetime.fromisoformat(created_at.replace('Z', '+00:00'))
                time_text = dt.strftime('%Y-%m-%d %H:%M:%S')
            except:
                time_text = created_at
        else:
            time_text = 'غير محدد'
        time_label = QLabel(time_text)
        time_label.setStyleSheet("font-weight: bold;")
        details_layout.addWidget(time_label, 4, 1)
        
        content_layout.addLayout(details_layout)
        
        # Separator
        separator = QFrame()
        separator.setFrameShape(QFrame.HLine)
        separator.setStyleSheet("color: #dee2e6;")
        content_layout.addWidget(separator)
        
        # Items table
        self.create_items_table(content_layout)
        
        # Notes if any
        if self.order_data.get('notes'):
            notes_label = QLabel(f"ملاحظات: {self.order_data.get('notes')}")
            notes_label.setStyleSheet("font-style: italic; color: #6c757d; margin-top: 10px;")
            content_layout.addWidget(notes_label)
        
        parent_layout.addWidget(content_frame)
    
    def create_items_table(self, parent_layout):
        """Create items table"""
        items_table = QTableWidget()
        items_table.setColumnCount(4)
        items_table.setHorizontalHeaderLabels(["الصنف", "الكمية", "السعر", "المجموع"])
        
        items = self.order_data.get('items', [])
        items_table.setRowCount(len(items))
        
        total_amount = 0
        
        for row, item in enumerate(items):
            # Item name
            items_table.setItem(row, 0, QTableWidgetItem(item.get('name', '')))
            
            # Quantity
            quantity = item.get('quantity', 0)
            items_table.setItem(row, 1, QTableWidgetItem(str(quantity)))
            
            # Price
            price = item.get('price', 0)
            price_text = f"{int(price):,}".replace(',', '.') + " دينار"
            items_table.setItem(row, 2, QTableWidgetItem(price_text))
            
            # Total
            item_total = quantity * price
            total_amount += item_total
            total_text = f"{int(item_total):,}".replace(',', '.') + " دينار"
            items_table.setItem(row, 3, QTableWidgetItem(total_text))
        
        # Style the table
        items_table.setStyleSheet("""
            QTableWidget {
                border: 1px solid #dee2e6;
                border-radius: 5px;
                gridline-color: #dee2e6;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #dee2e6;
            }
            QHeaderView::section {
                background-color: #f8f9fa;
                padding: 10px;
                border: none;
                font-weight: bold;
            }
        """)
        
        # Resize columns
        header = items_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Stretch)
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)
        
        items_table.verticalHeader().setVisible(False)
        items_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        
        parent_layout.addWidget(items_table)
        
        # Total amount
        total_frame = QFrame()
        total_layout = QHBoxLayout(total_frame)
        total_layout.addStretch()
        
        total_label = QLabel(f"المجموع الكلي: {int(total_amount):,}".replace(',', '.') + " دينار")
        total_label.setFont(QFont("Arial", 16, QFont.Bold))
        total_label.setStyleSheet("color: #1E2A38; padding: 10px; background-color: #f8f9fa; border-radius: 5px;")
        total_layout.addWidget(total_label)
        
        parent_layout.addWidget(total_frame)
    
    def create_footer(self, parent_layout):
        """Create footer with action buttons"""
        footer_layout = QHBoxLayout()
        
        # Back button
        back_btn = QPushButton("🔙 العودة")
        back_btn.setFont(QFont("Arial", 12, QFont.Bold))
        back_btn.setFixedSize(120, 45)
        back_btn.setStyleSheet("""
            QPushButton {
                background-color: #6c757d;
                color: white;
                border: none;
                border-radius: 8px;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #5a6268;
            }
        """)
        back_btn.clicked.connect(self.go_back)
        footer_layout.addWidget(back_btn)
        
        footer_layout.addStretch()
        
        # Print button
        print_btn = QPushButton("🖨️ طباعة")
        print_btn.setFont(QFont("Arial", 12, QFont.Bold))
        print_btn.setFixedSize(120, 45)
        print_btn.setStyleSheet("""
            QPushButton {
                background-color: #17a2b8;
                color: white;
                border: none;
                border-radius: 8px;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #138496;
            }
        """)
        print_btn.clicked.connect(self.print_invoice)
        footer_layout.addWidget(print_btn)
        
        # Approval buttons (only in approval mode)
        if self.approval_mode:
            # Reject button
            reject_btn = QPushButton("❌ رفض")
            reject_btn.setFont(QFont("Arial", 12, QFont.Bold))
            reject_btn.setFixedSize(120, 45)
            reject_btn.setStyleSheet("""
                QPushButton {
                    background-color: #dc3545;
                    color: white;
                    border: none;
                    border-radius: 8px;
                    font-size: 12px;
                }
                QPushButton:hover {
                    background-color: #c82333;
                }
            """)
            reject_btn.clicked.connect(self.reject_order)
            footer_layout.addWidget(reject_btn)
            
            # Approve button
            approve_btn = QPushButton("✅ موافقة")
            approve_btn.setFont(QFont("Arial", 12, QFont.Bold))
            approve_btn.setFixedSize(120, 45)
            approve_btn.setStyleSheet("""
                QPushButton {
                    background-color: #28a745;
                    color: white;
                    border: none;
                    border-radius: 8px;
                    font-size: 12px;
                }
                QPushButton:hover {
                    background-color: #218838;
                }
            """)
            approve_btn.clicked.connect(self.approve_order)
            footer_layout.addWidget(approve_btn)
        
        parent_layout.addLayout(footer_layout)
    
    def get_order_type_text(self, order_type):
        """Get Arabic text for order type"""
        types = {
            'dine-in': 'طاولات',
            'takeaway': 'سفري',
            'delivery': 'دلفري'
        }
        return types.get(order_type, order_type)
    
    def go_back(self):
        """Go back to previous screen"""
        self.main_app.show_pending_orders()
    
    def print_invoice(self):
        """Print the invoice"""
        from gui.printer_selector import PrinterSelectorDialog
        
        # Show printer selection dialog
        printer_dialog = PrinterSelectorDialog(self.main_app)
        if printer_dialog.exec_() == QDialog.Accepted:
            selected_printers = printer_dialog.get_selected_printers()
            
            if selected_printers:
                # Print to selected printers
                for printer_name in selected_printers:
                    self.print_to_printer(printer_name)
                    
                QMessageBox.information(self, "طباعة", f"تم إرسال الطلب للطباعة على {len(selected_printers)} طابعة")
    
    def print_to_printer(self, printer_name):
        """Print to specific printer"""
        try:
            # Create receipt content
            receipt_content = self.create_receipt_content()
            
            # Print using the printer manager
            if hasattr(self.main_app, 'printer_manager'):
                self.main_app.printer_manager.print_receipt(receipt_content, printer_name)
            else:
                print(f"Printing to {printer_name}:")
                print(receipt_content)
                
        except Exception as e:
            print(f"Error printing to {printer_name}: {e}")
    
    def create_receipt_content(self):
        """Create receipt content"""
        content = []
        content.append("=" * 40)
        content.append("مطعم النوفوس")
        content.append("=" * 40)
        content.append(f"طلب #{self.order_data.get('orderNumber', 'N/A')}")
        content.append(f"النادل: {self.order_data.get('waiterName', 'غير محدد')}")
        content.append(f"النوع: {self.get_order_type_text(self.order_data.get('type', ''))}")
        
        if self.order_data.get('type') == 'dine-in':
            content.append(f"طاولة: {self.order_data.get('tableNumber', 'غير محدد')}")
        elif self.order_data.get('type') == 'delivery':
            content.append(f"هاتف: {self.order_data.get('customerPhone', '')}")
            content.append(f"العنوان: {self.order_data.get('customerAddress', '')}")
        
        content.append("-" * 40)
        content.append("الأصناف:")
        
        total = 0
        for item in self.order_data.get('items', []):
            name = item.get('name', '')
            quantity = item.get('quantity', 0)
            price = item.get('price', 0)
            item_total = quantity * price
            total += item_total
            
            content.append(f"{name}")
            content.append(f"  {quantity} × {int(price):,}".replace(',', '.') + f" = {int(item_total):,}".replace(',', '.'))
        
        content.append("-" * 40)
        content.append(f"المجموع: {int(total):,}".replace(',', '.') + " دينار")
        
        if self.order_data.get('notes'):
            content.append("-" * 40)
            content.append(f"ملاحظات: {self.order_data.get('notes')}")
        
        content.append("=" * 40)
        content.append("شكراً لكم")
        content.append("=" * 40)
        
        return "\n".join(content)
    
    def approve_order(self):
        """Approve the order"""
        reply = QMessageBox.question(
            self, 
            "تأكيد الموافقة",
            f"هل أنت متأكد من الموافقة على الطلب #{self.order_data.get('orderNumber')}؟",
            QMessageBox.Yes | QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            try:
                response = requests.post(
                    f"http://localhost:3001/api/waiter-orders/{self.order_data['id']}/approve",
                    timeout=5
                )
                if response.status_code == 200:
                    QMessageBox.information(self, "نجح", "تم الموافقة على الطلب بنجاح")
                    self.go_back()
                else:
                    QMessageBox.critical(self, "خطأ", "فشل في الموافقة على الطلب")
            except requests.exceptions.RequestException as e:
                QMessageBox.critical(self, "خطأ", "خطأ في الاتصال")
    
    def reject_order(self):
        """Reject the order"""
        reason, ok = QInputDialog.getText(
            self, 
            "سبب الرفض", 
            f"أدخل سبب رفض الطلب #{self.order_data.get('orderNumber')}:"
        )
        
        if ok and reason.strip():
            try:
                response = requests.post(
                    f"http://localhost:3001/api/waiter-orders/{self.order_data['id']}/reject",
                    json={'reason': reason.strip()},
                    timeout=5
                )
                if response.status_code == 200:
                    QMessageBox.information(self, "نجح", "تم رفض الطلب")
                    self.go_back()
                else:
                    QMessageBox.critical(self, "خطأ", "فشل في رفض الطلب")
            except requests.exceptions.RequestException as e:
                QMessageBox.critical(self, "خطأ", "خطأ في الاتصال")
