{"name": "waiter-app", "version": "1.0.0", "description": "Waiter mobile app for restaurant orders", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js"}, "dependencies": {"axios": "^1.9.0", "cors": "^2.8.5", "express": "^4.18.2", "moment": "^2.29.4", "socket.io": "^4.7.2", "uuid": "^9.0.0"}, "devDependencies": {"nodemon": "^3.0.1"}, "keywords": ["restaurant", "waiter", "orders", "mobile"], "author": "Restaurant System", "license": "MIT"}