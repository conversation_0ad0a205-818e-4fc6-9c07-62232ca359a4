from PyQt5.QtWidgets import (QW<PERSON>t, QVBoxLayout, QHBoxLayout, QLabel, 
                             QPushButton, QFrame, QLineEdit, QMessageBox)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

class DeliveryWindow(QWidget):
    def __init__(self, main_app):
        super().__init__()
        self.main_app = main_app
        self.setup_ui()
    
    def setup_ui(self):
        """Set up the delivery UI"""
        layout = QVBoxLayout()
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)
        
        # Top bar
        self.create_top_bar(layout)
        
        # Main content
        main_content = QWidget()
        main_layout = QVBoxLayout(main_content)
        main_layout.setAlignment(Qt.AlignCenter)
        main_layout.setSpacing(30)
        main_layout.setContentsMargins(50, 50, 50, 50)
        
        # Title
        title = QLabel("طلبات الدلفري")
        title.setAlignment(Qt.AlignCenter)
        title.setFont(QFont("Arial", 24, QFont.Bold))
        title.setStyleSheet("color: #1E2A38;")
        main_layout.addWidget(title)
        
        # Customer info form
        form_widget = QWidget()
        form_widget.setFixedWidth(400)
        form_layout = QVBoxLayout(form_widget)
        form_layout.setSpacing(15)
        
        # Customer phone
        phone_label = QLabel("رقم الزبون:")
        phone_label.setFont(QFont("Arial", 14, QFont.Bold))
        phone_label.setStyleSheet("color: #1E2A38;")
        form_layout.addWidget(phone_label)
        
        self.phone_input = QLineEdit()
        self.phone_input.setPlaceholderText("أدخل رقم هاتف الزبون")
        self.phone_input.setFont(QFont("Arial", 12))
        self.phone_input.setFixedHeight(40)
        self.phone_input.setLayoutDirection(Qt.LeftToRight)
        self.phone_input.setAlignment(Qt.AlignLeft)
        self.phone_input.setStyleSheet("""
            QLineEdit {
                padding: 8px;
                border: 2px solid #ddd;
                border-radius: 8px;
                font-size: 14px;
                background-color: white;
                color: black;
            }
            QLineEdit:focus {
                border-color: #1E2A38;
                background-color: #f8f9fa;
            }
        """)
        form_layout.addWidget(self.phone_input)
        
        # Customer address
        address_label = QLabel("العنوان:")
        address_label.setFont(QFont("Arial", 14, QFont.Bold))
        address_label.setStyleSheet("color: #1E2A38;")
        form_layout.addWidget(address_label)
        
        self.address_input = QLineEdit()
        self.address_input.setPlaceholderText("أدخل عنوان التوصيل")
        self.address_input.setFont(QFont("Arial", 12))
        self.address_input.setFixedHeight(40)
        self.address_input.setStyleSheet("""
            QLineEdit {
                padding: 8px;
                border: 2px solid #ddd;
                border-radius: 8px;
                font-size: 14px;
                background-color: white;
                color: black;
            }
            QLineEdit:focus {
                border-color: #1E2A38;
                background-color: #f8f9fa;
            }
        """)
        form_layout.addWidget(self.address_input)
        
        main_layout.addWidget(form_widget, alignment=Qt.AlignCenter)
        
        # Start order button
        start_order_btn = QPushButton("بدء طلب دلفري")
        start_order_btn.setFixedSize(300, 60)
        start_order_btn.setFont(QFont("Arial", 16, QFont.Bold))
        start_order_btn.setStyleSheet("""
            QPushButton {
                background-color: #1E2A38;
                color: white;
                border: none;
                border-radius: 15px;
            }
            QPushButton:hover {
                background-color: #D4AF37;
            }
        """)
        start_order_btn.clicked.connect(self.start_delivery_order)
        main_layout.addWidget(start_order_btn, alignment=Qt.AlignCenter)
        
        layout.addWidget(main_content)
        
        # Footer navigation
        self.create_footer_navigation(layout)
        
        self.setLayout(layout)
    
    def create_top_bar(self, parent_layout):
        """Create top navigation bar"""
        top_bar = QFrame()
        top_bar.setFixedHeight(80)
        top_bar.setStyleSheet("""
            QFrame {
                background-color: #1E2A38;
                border-bottom: 3px solid #D4AF37;
            }
        """)
        
        top_layout = QHBoxLayout(top_bar)
        top_layout.setContentsMargins(20, 10, 20, 10)
        
        # Notifications icon (left)
        notifications_btn = QPushButton("🔔")
        notifications_btn.setFixedSize(50, 50)
        notifications_btn.setStyleSheet("""
            QPushButton {
                background-color: transparent;
                color: #D4AF37;
                border: 2px solid #D4AF37;
                border-radius: 25px;
                font-size: 20px;
            }
            QPushButton:hover {
                background-color: #D4AF37;
                color: #1E2A38;
            }
        """)
        top_layout.addWidget(notifications_btn)
        
        # Center area
        center_widget = QWidget()
        center_layout = QVBoxLayout(center_widget)
        center_layout.setAlignment(Qt.AlignCenter)
        
        page_title = QLabel("الدلفري")
        page_title.setAlignment(Qt.AlignCenter)
        page_title.setFont(QFont("Arial", 18, QFont.Bold))
        page_title.setStyleSheet("color: white;")
        center_layout.addWidget(page_title)
        
        top_layout.addWidget(center_widget)
        
        # Settings icon (right)
        settings_btn = QPushButton("⚙️")
        settings_btn.setFixedSize(50, 50)
        settings_btn.setStyleSheet("""
            QPushButton {
                background-color: transparent;
                color: #D4AF37;
                border: 2px solid #D4AF37;
                border-radius: 25px;
                font-size: 20px;
            }
            QPushButton:hover {
                background-color: #D4AF37;
                color: #1E2A38;
            }
        """)
        top_layout.addWidget(settings_btn)
        
        parent_layout.addWidget(top_bar)
    
    def create_footer_navigation(self, parent_layout):
        """Create footer navigation bar"""
        footer = QFrame()
        footer.setFixedHeight(70)
        footer.setStyleSheet("""
            QFrame {
                background-color: #1E2A38;
                border-top: 2px solid #D4AF37;
            }
        """)
        
        footer_layout = QHBoxLayout(footer)
        footer_layout.setContentsMargins(20, 10, 20, 10)
        footer_layout.setSpacing(20)
        
        # Back button
        back_btn = QPushButton("🔙 العودة")
        back_btn.setFont(QFont("Arial", 12, QFont.Bold))
        back_btn.setStyleSheet("""
            QPushButton {
                background-color: transparent;
                color: #D4AF37;
                border: 2px solid #D4AF37;
                border-radius: 8px;
                padding: 8px 16px;
            }
            QPushButton:hover {
                background-color: #D4AF37;
                color: #1E2A38;
            }
        """)
        back_btn.clicked.connect(self.main_app.show_dashboard)
        footer_layout.addWidget(back_btn)
        
        footer_layout.addStretch()
        
        # Old invoices button
        invoices_btn = QPushButton("📜 عرض الفواتير القديمة")
        invoices_btn.setFont(QFont("Arial", 12, QFont.Bold))
        invoices_btn.setStyleSheet("""
            QPushButton {
                background-color: transparent;
                color: #D4AF37;
                border: 2px solid #D4AF37;
                border-radius: 8px;
                padding: 8px 16px;
            }
            QPushButton:hover {
                background-color: #D4AF37;
                color: #1E2A38;
            }
        """)
        invoices_btn.clicked.connect(self.main_app.show_invoices)
        footer_layout.addWidget(invoices_btn)
        
        # Sales summary button
        sales_btn = QPushButton("📊 مبيعات اليوم")
        sales_btn.setFont(QFont("Arial", 12, QFont.Bold))
        sales_btn.setStyleSheet("""
            QPushButton {
                background-color: transparent;
                color: #D4AF37;
                border: 2px solid #D4AF37;
                border-radius: 8px;
                padding: 8px 16px;
            }
            QPushButton:hover {
                background-color: #D4AF37;
                color: #1E2A38;
            }
        """)
        sales_btn.clicked.connect(self.main_app.show_sales)
        footer_layout.addWidget(sales_btn)
        
        parent_layout.addWidget(footer)
    
    def start_delivery_order(self):
        """Start new delivery order"""
        phone = self.phone_input.text().strip()
        address = self.address_input.text().strip()
        
        if not phone or not address:
            QMessageBox.warning(self, "تحذير", "يرجى إدخال رقم الهاتف والعنوان")
            return
        
        # Store delivery info and show menu
        self.main_app.delivery_phone = phone
        self.main_app.delivery_address = address
        self.main_app.show_menu('delivery')
    
    def showEvent(self, event):
        """Called when window is shown"""
        super().showEvent(event)
        self.phone_input.clear()
        self.address_input.clear()
        self.phone_input.setFocus()

    def load_waiter_order(self, order):
        """Load waiter order for editing"""
        try:
            from gui.order import OrderWindow

            # Pre-fill delivery info if available
            if order.get('customerPhone'):
                self.phone_input.setText(order.get('customerPhone'))
            if order.get('customerAddress'):
                self.address_input.setText(order.get('customerAddress'))

            # Store delivery info
            self.main_app.delivery_phone = order.get('customerPhone', '')
            self.main_app.delivery_address = order.get('customerAddress', '')

            # Create order window for delivery
            order_window = OrderWindow(self.main_app, 'delivery')

            # Load waiter order data
            order_window.load_waiter_order_data(order)

            order_window.show()

        except Exception as e:
            print(f"Error loading waiter order: {e}")
            QMessageBox.critical(self, "خطأ", f"خطأ في تحميل طلب النادل: {str(e)}")
