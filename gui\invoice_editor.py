from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
                             QPushButton, QFrame, QListWidget, QListWidgetItem,
                             QMessageBox, QTextEdit, QWidget)
from PyQt5.QtCore import Qt, QThread, pyqtSignal
from PyQt5.QtGui import QFont

class OrderUpdateThread(QThread):
    """Thread for updating order"""
    order_updated = pyqtSignal(dict)
    
    def __init__(self, api_client, order_id, order_data):
        super().__init__()
        self.api_client = api_client
        self.order_id = order_id
        self.order_data = order_data
    
    def run(self):
        # Since we don't have an update endpoint, we'll create a new order with modified flag
        self.order_data['isModified'] = True
        self.order_data['originalOrderId'] = self.order_id
        result = self.api_client.create_order(self.order_data)
        self.order_updated.emit(result)

class InvoiceEditorDialog(QDialog):
    def __init__(self, invoice, main_app, parent=None):
        super().__init__(parent)
        self.invoice = invoice
        self.main_app = main_app
        self.order_items = []
        
        # Copy original items
        for item in invoice.get('items', []):
            self.order_items.append({
                'item': {
                    'id': item.get('id'),
                    'name': item.get('name'),
                    'price': item.get('price')
                },
                'quantity': item.get('quantity')
            })
        
        self.setWindowTitle(f"تعديل الفاتورة رقم {invoice.get('orderNumber', '')}")
        self.setFixedSize(500, 600)
        self.setModal(True)
        self.setup_ui()
    
    def setup_ui(self):
        """Set up the invoice editor UI"""
        layout = QVBoxLayout()
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)
        
        # Title
        title = QLabel(f"تعديل الفاتورة رقم {self.invoice.get('orderNumber', '')}")
        title.setAlignment(Qt.AlignCenter)
        title.setFont(QFont("Arial", 18, QFont.Bold))
        title.setStyleSheet("color: #1E2A38; margin-bottom: 20px;")
        layout.addWidget(title)
        
        # Warning
        warning = QLabel("⚠️ تعديل الطلب سيظهر كطلب جديد مع علامة 'تم التعديل'")
        warning.setAlignment(Qt.AlignCenter)
        warning.setFont(QFont("Arial", 12))
        warning.setStyleSheet("""
            color: #856404;
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 5px;
            padding: 10px;
            margin-bottom: 15px;
        """)
        layout.addWidget(warning)
        
        # Order items list
        items_label = QLabel("أصناف الطلب:")
        items_label.setFont(QFont("Arial", 14, QFont.Bold))
        items_label.setStyleSheet("color: #1E2A38;")
        layout.addWidget(items_label)
        
        self.order_list = QListWidget()
        self.order_list.setMinimumHeight(250)
        self.order_list.setStyleSheet("""
            QListWidget {
                border: 2px solid #ddd;
                border-radius: 8px;
                background-color: white;
                font-size: 13px;
            }
            QListWidget::item {
                padding: 12px;
                border-bottom: 1px solid #eee;
                min-height: 40px;
            }
            QListWidget::item:selected {
                background-color: #e3f2fd;
            }
        """)
        layout.addWidget(self.order_list)
        
        # Total amount
        self.total_label = QLabel("المجموع: 0.00 دينار")
        self.total_label.setFont(QFont("Arial", 16, QFont.Bold))
        self.total_label.setStyleSheet("""
            color: #1E2A38; 
            padding: 10px; 
            background-color: #f8f9fa; 
            border-radius: 5px;
            text-align: center;
        """)
        self.total_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(self.total_label)
        
        # Notes
        notes_label = QLabel("ملاحظات:")
        notes_label.setFont(QFont("Arial", 12, QFont.Bold))
        notes_label.setStyleSheet("color: #1E2A38;")
        layout.addWidget(notes_label)
        
        self.notes_text = QTextEdit()
        self.notes_text.setFixedHeight(80)
        self.notes_text.setPlainText(self.invoice.get('notes', ''))
        self.notes_text.setStyleSheet("""
            QTextEdit {
                border: 2px solid #ddd;
                border-radius: 5px;
                padding: 8px;
            }
        """)
        layout.addWidget(self.notes_text)
        
        # Buttons
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(10)
        
        save_btn = QPushButton("حفظ التعديلات")
        save_btn.setFont(QFont("Arial", 12, QFont.Bold))
        save_btn.setFixedHeight(40)
        save_btn.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                border: none;
                border-radius: 8px;
                padding: 10px 20px;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        save_btn.clicked.connect(self.save_changes)
        buttons_layout.addWidget(save_btn)
        
        cancel_btn = QPushButton("إلغاء")
        cancel_btn.setFont(QFont("Arial", 12, QFont.Bold))
        cancel_btn.setFixedHeight(40)
        cancel_btn.setStyleSheet("""
            QPushButton {
                background-color: #6c757d;
                color: white;
                border: none;
                border-radius: 8px;
                padding: 10px 20px;
            }
            QPushButton:hover {
                background-color: #545b62;
            }
        """)
        cancel_btn.clicked.connect(self.reject)
        buttons_layout.addWidget(cancel_btn)
        
        layout.addLayout(buttons_layout)
        self.setLayout(layout)
        
        # Update display
        self.update_order_summary()
    
    def update_order_summary(self):
        """Update order summary display"""
        self.order_list.clear()
        total_amount = 0
        
        for i, order_item in enumerate(self.order_items):
            item = order_item['item']
            quantity = order_item['quantity']
            item_total = item['price'] * quantity
            total_amount += item_total
            
            # Create custom widget for order item
            item_widget = QWidget()
            item_layout = QHBoxLayout(item_widget)
            item_layout.setContentsMargins(5, 5, 5, 5)
            item_layout.setSpacing(10)
            
            # Item info
            if quantity == 0.5:
                qty_text = "½"
            elif quantity == 0.25:
                qty_text = "¼"
            elif quantity == int(quantity):
                qty_text = str(int(quantity))
            else:
                qty_text = f"{quantity:.2f}"
            
            # Format prices as Iraqi currency
            price = int(item['price'])
            total = int(item_total)
            formatted_price = f"{price:,}".replace(',', '.')
            formatted_total = f"{total:,}".replace(',', '.')

            item_info = QLabel(f"{item['name']}\n{qty_text} × {formatted_price} = {formatted_total} دينار")
            item_info.setFont(QFont("Arial", 11))
            item_info.setStyleSheet("color: #333; padding: 5px;")
            item_layout.addWidget(item_info)
            
            # Delete button
            delete_btn = QPushButton("🗑️")
            delete_btn.setFixedSize(30, 30)
            delete_btn.setFont(QFont("Arial", 12))
            delete_btn.setStyleSheet("""
                QPushButton {
                    background-color: #dc3545;
                    color: white;
                    border: none;
                    border-radius: 15px;
                    font-size: 14px;
                }
                QPushButton:hover {
                    background-color: #c82333;
                    transform: scale(1.1);
                }
            """)
            delete_btn.clicked.connect(lambda checked, idx=i: self.remove_order_item(idx))
            item_layout.addWidget(delete_btn)
            
            # Add to list
            list_item = QListWidgetItem()
            list_item.setSizeHint(item_widget.sizeHint())
            self.order_list.addItem(list_item)
            self.order_list.setItemWidget(list_item, item_widget)
        
        # Format total as Iraqi currency
        total = int(total_amount)
        formatted_total = f"{total:,}".replace(',', '.')
        self.total_label.setText(f"المجموع: {formatted_total} دينار")
    
    def remove_order_item(self, index):
        """Remove item from order"""
        if 0 <= index < len(self.order_items):
            self.order_items.pop(index)
            self.update_order_summary()
    
    def save_changes(self):
        """Save changes to order"""
        if not self.order_items:
            QMessageBox.warning(self, "تحذير", "لا يمكن حفظ طلب فارغ")
            return
        
        # Prepare order data
        order_data = {
            'type': self.invoice.get('type'),
            'items': [
                {
                    'id': order_item['item']['id'],
                    'name': order_item['item']['name'],
                    'price': order_item['item']['price'],
                    'quantity': order_item['quantity']
                }
                for order_item in self.order_items
            ],
            'notes': f"[تم التعديل] {self.notes_text.toPlainText().strip()}",
            'isModified': True,
            'originalOrderNumber': self.invoice.get('orderNumber')
        }
        
        # Add type-specific data
        if self.invoice.get('type') == 'dine-in':
            order_data['hallId'] = self.invoice.get('hallId')
            order_data['tableNumber'] = self.invoice.get('tableNumber')
        elif self.invoice.get('type') == 'delivery':
            order_data['customerPhone'] = self.invoice.get('customerPhone')
            order_data['customerAddress'] = self.invoice.get('customerAddress')
        
        # Create modified order
        self.update_thread = OrderUpdateThread(self.main_app.api_client, self.invoice['id'], order_data)
        self.update_thread.order_updated.connect(self.handle_order_updated)
        self.update_thread.start()
    
    def handle_order_updated(self, result):
        """Handle order update result"""
        if 'error' in result:
            QMessageBox.critical(self, "خطأ", f"فشل في حفظ التعديلات: {result['error']}")
            return
        
        if 'order' in result:
            QMessageBox.information(self, "نجح", "تم حفظ التعديلات بنجاح كطلب جديد")
            self.accept()
