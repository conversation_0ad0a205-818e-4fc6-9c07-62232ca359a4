{"name": "node-thermal-printer", "version": "4.5.0", "description": "Print on Epson, Star, Tranca, Daruma, Brother and Custom thermal printers with Node.js", "main": "node-thermal-printer.js", "repository": {"type": "git", "url": "https://github.com/Klemen1337/node-thermal-printer"}, "keywords": ["thermal printer", "thermal", "printer", "epson", "star", "daruma", "tranca", "brother", "custom"], "author": "<PERSON><PERSON><PERSON> <<EMAIL>> (http://kastelic.net/)", "license": "ISC", "bugs": {"url": "https://github.com/Klemen1337/node-thermal-printer/issues"}, "homepage": "https://github.com/Klemen1337/node-thermal-printer", "dependencies": {"iconv-lite": "0.6.3", "pngjs": "7.0.0", "unorm": "1.6.0", "write-file-queue": "0.0.1"}, "devDependencies": {"@eslint/js": "^9.20.0", "@types/node": "^10.14.2", "eslint": "^9.20.1", "globals": "^15.15.0"}}