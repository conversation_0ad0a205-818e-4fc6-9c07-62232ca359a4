@echo off
chcp 65001 >nul
title نظام إدارة مطعم النوفوس - تجميع وتشغيل مشغل النظام
echo ========================================
echo    نظام إدارة مطعم النوفوس - مشغل النظام C
echo    Restaurant Management System - C Launcher
echo ========================================
echo.
echo    شركة الباش البرمجية - Al-Bash Software Company
echo    الهاتف/Phone: 07715086335
echo    واتساب/WhatsApp: 07715086335
echo    الموقع/Website: www.albash-software.com
echo.
echo ========================================
echo.

echo [1/3] فحص وجود مترجم C...
echo Checking C compiler...

:: Check for GCC
gcc --version >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ تم العثور على GCC
    echo ✅ GCC found
    set COMPILER=gcc
    goto compile
)

:: Check for Visual Studio compiler
cl >nul 2>&1
if %errorlevel% neq 9009 (
    echo ✅ تم العثور على Visual Studio Compiler
    echo ✅ Visual Studio Compiler found
    set COMPILER=cl
    goto compile_vs
)

:: No compiler found
echo ❌ لم يتم العثور على مترجم C!
echo ❌ C compiler not found!
echo.
echo يرجى تثبيت أحد المترجمات التالية:
echo Please install one of the following compilers:
echo.
echo 1. MinGW-w64: https://www.mingw-w64.org/
echo 2. Visual Studio: https://visualstudio.microsoft.com/
echo 3. Code::Blocks: https://www.codeblocks.org/
echo.
pause
exit /b 1

:compile
echo.
echo [2/3] تجميع البرنامج باستخدام GCC...
echo Compiling program using GCC...

gcc -Wall -Wextra -std=c99 -o system_launcher.exe system_launcher.c -lws2_32

if %errorlevel% neq 0 (
    echo ❌ فشل في تجميع البرنامج
    echo ❌ Failed to compile program
    pause
    exit /b 1
)

echo ✅ تم تجميع البرنامج بنجاح!
echo ✅ Program compiled successfully!
goto run_program

:compile_vs
echo.
echo [2/3] تجميع البرنامج باستخدام Visual Studio...
echo Compiling program using Visual Studio...

cl /Fe:system_launcher.exe system_launcher.c ws2_32.lib

if %errorlevel% neq 0 (
    echo ❌ فشل في تجميع البرنامج
    echo ❌ Failed to compile program
    pause
    exit /b 1
)

echo ✅ تم تجميع البرنامج بنجاح!
echo ✅ Program compiled successfully!

:: Clean up Visual Studio temporary files
if exist system_launcher.obj del system_launcher.obj

:run_program
echo.
echo [3/3] تشغيل مشغل النظام...
echo Running system launcher...
echo.

if not exist system_launcher.exe (
    echo ❌ ملف البرنامج غير موجود!
    echo ❌ Program file not found!
    pause
    exit /b 1
)

echo ========================================
echo 🚀 تشغيل مشغل النظام المكتوب بلغة C
echo 🚀 Running C System Launcher
echo ========================================
echo.

:: Run the compiled program
system_launcher.exe

echo.
echo ========================================
echo تم إنهاء مشغل النظام
echo System launcher finished
echo ========================================
echo.
echo الملف المجمع: system_launcher.exe
echo Compiled file: system_launcher.exe
echo.
echo يمكنك تشغيل البرنامج مباشرة بالنقر على:
echo You can run the program directly by clicking:
echo system_launcher.exe
echo.
echo شركة الباش البرمجية - Al-Bash Software Company
echo الهاتف/واتساب: 07715086335
echo Phone/WhatsApp: 07715086335
echo.
pause
