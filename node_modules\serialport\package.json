{"name": "serialport", "version": "12.0.0", "description": "Node.js package to access serial ports. Linux, OSX and Windows. Welcome your robotic JavaScript overlords. Better yet, program them!", "main": "./dist/index.js", "types": "./dist/index.d.ts", "scripts": {"build": "tsc --build tsconfig-build.json"}, "repository": {"type": "git", "url": "git://github.com/serialport/node-serialport.git"}, "keywords": ["ccTalk", "com port", "COM", "data logging", "hardware", "iot", "johnny-five", "modem", "nodebots", "RFID", "robotics", "sensor", "serial port", "serial", "serialport", "sms gateway", "sms", "stream", "tty", "UART"], "maintainers": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "https://www.roborooter.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://hipsterbrown.com/"}, {"name": "Maybe you? Come and help out!", "url": "https://github.com/node-serialport"}], "dependencies": {"@serialport/binding-mock": "10.2.2", "@serialport/bindings-cpp": "12.0.1", "@serialport/parser-byte-length": "12.0.0", "@serialport/parser-cctalk": "12.0.0", "@serialport/parser-delimiter": "12.0.0", "@serialport/parser-inter-byte-timeout": "12.0.0", "@serialport/parser-packet-length": "12.0.0", "@serialport/parser-readline": "12.0.0", "@serialport/parser-ready": "12.0.0", "@serialport/parser-regex": "12.0.0", "@serialport/parser-slip-encoder": "12.0.0", "@serialport/parser-spacepacket": "12.0.0", "@serialport/stream": "12.0.0", "debug": "4.3.4"}, "engines": {"node": ">=16.0.0"}, "license": "MIT", "funding": "https://opencollective.com/serialport/donate", "preferUnplugged": false, "devDependencies": {"typescript": "5.2.2"}, "gitHead": "f7e7bd53f9578a26c4f44cc1949fef396dc064c7"}