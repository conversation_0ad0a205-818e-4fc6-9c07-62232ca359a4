from PyQt5.QtWidgets import (Q<PERSON><PERSON>t, QVBoxLayout, QHBoxLayout, QLabel,
                             QPushButton, QFrame, QScrollArea, QGridLayout,
                             QMessageBox, QInputDialog, QListWidget, QListWidgetItem,
                             QSpinBox, QTextEdit, QLineEdit)
from PyQt5.QtCore import Qt, QThread, pyqtSignal
from PyQt5.QtGui import QFont

class MenuLoadThread(QThread):
    """Thread for loading menu data"""
    data_loaded = pyqtSignal(dict)
    
    def __init__(self, api_client):
        super().__init__()
        self.api_client = api_client
    
    def run(self):
        result = self.api_client.get_menu()
        self.data_loaded.emit(result)

class OrderCreateThread(QThread):
    """Thread for creating order"""
    order_created = pyqtSignal(dict)
    
    def __init__(self, api_client, order_data):
        super().__init__()
        self.api_client = api_client
        self.order_data = order_data
    
    def run(self):
        result = self.api_client.create_order(self.order_data)
        self.order_created.emit(result)

class MenuWindow(QWidget):
    def __init__(self, main_app):
        super().__init__()
        self.main_app = main_app
        self.menu_data = []
        self.current_section = None
        self.order_items = []  # List of {item, quantity}
        self.order_type = 'dine-in'
        self.hall_id = None
        self.table_number = None
        self.setup_ui()
    
    def setup_ui(self):
        """Set up the menu UI"""
        layout = QVBoxLayout()
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)
        
        # Top bar
        self.create_top_bar(layout)
        
        # Main content area
        main_content = QHBoxLayout()
        
        # Right side - Menu sections and items
        self.create_menu_area(main_content)
        
        # Center - Number pad
        self.create_number_pad(main_content)
        
        # Left side - Order summary
        self.create_order_summary(main_content)
        
        main_widget = QWidget()
        main_widget.setLayout(main_content)
        layout.addWidget(main_widget)
        
        # Footer navigation
        self.create_footer_navigation(layout)
        
        self.setLayout(layout)
    
    def create_top_bar(self, parent_layout):
        """Create top navigation bar"""
        top_bar = QFrame()
        top_bar.setFixedHeight(80)
        top_bar.setStyleSheet("""
            QFrame {
                background-color: #1E2A38;
                border-bottom: 3px solid #D4AF37;
            }
        """)
        
        top_layout = QHBoxLayout(top_bar)
        top_layout.setContentsMargins(20, 10, 20, 10)
        
        # Notifications icon (left)
        notifications_btn = QPushButton("🔔")
        notifications_btn.setFixedSize(50, 50)
        notifications_btn.setStyleSheet("""
            QPushButton {
                background-color: transparent;
                color: #D4AF37;
                border: 2px solid #D4AF37;
                border-radius: 25px;
                font-size: 20px;
            }
            QPushButton:hover {
                background-color: #D4AF37;
                color: #1E2A38;
            }
        """)
        top_layout.addWidget(notifications_btn)
        
        # Center area - Order info
        center_widget = QWidget()
        center_layout = QVBoxLayout(center_widget)
        center_layout.setAlignment(Qt.AlignCenter)
        center_layout.setSpacing(5)
        
        self.order_info_label = QLabel("قائمة الطعام")
        self.order_info_label.setAlignment(Qt.AlignCenter)
        self.order_info_label.setFont(QFont("Arial", 18, QFont.Bold))
        self.order_info_label.setStyleSheet("color: white;")
        center_layout.addWidget(self.order_info_label)
        
        top_layout.addWidget(center_widget)
        
        # Settings icon (right)
        settings_btn = QPushButton("⚙️")
        settings_btn.setFixedSize(50, 50)
        settings_btn.setStyleSheet("""
            QPushButton {
                background-color: transparent;
                color: #D4AF37;
                border: 2px solid #D4AF37;
                border-radius: 25px;
                font-size: 20px;
            }
            QPushButton:hover {
                background-color: #D4AF37;
                color: #1E2A38;
            }
        """)
        settings_btn.clicked.connect(self.show_settings)
        top_layout.addWidget(settings_btn)
        
        parent_layout.addWidget(top_bar)
    
    def create_menu_area(self, parent_layout):
        """Create menu sections and items area"""
        menu_widget = QWidget()
        menu_widget.setMinimumWidth(350)
        menu_widget.setMaximumWidth(450)
        menu_layout = QVBoxLayout(menu_widget)
        menu_layout.setContentsMargins(10, 10, 10, 10)
        menu_layout.setSpacing(10)
        
        # Sections list
        sections_label = QLabel("الأقسام:")
        sections_label.setFont(QFont("Arial", 14, QFont.Bold))
        sections_label.setStyleSheet("color: #1E2A38;")
        menu_layout.addWidget(sections_label)
        
        self.sections_list = QListWidget()
        self.sections_list.setFixedHeight(150)
        self.sections_list.setStyleSheet("""
            QListWidget {
                border: 2px solid #ddd;
                border-radius: 8px;
                background-color: white;
            }
            QListWidget::item {
                padding: 8px;
                border-bottom: 1px solid #eee;
            }
            QListWidget::item:selected {
                background-color: #1E2A38;
                color: white;
            }
        """)
        self.sections_list.itemClicked.connect(self.select_section)
        menu_layout.addWidget(self.sections_list)
        
        # Add section button
        add_section_btn = QPushButton("➕ إضافة قسم")
        add_section_btn.setFont(QFont("Arial", 10))
        add_section_btn.setStyleSheet("""
            QPushButton {
                background-color: #D4AF37;
                color: white;
                border: none;
                border-radius: 5px;
                padding: 5px 10px;
            }
            QPushButton:hover {
                background-color: #B8941F;
            }
        """)
        add_section_btn.clicked.connect(self.add_section)
        menu_layout.addWidget(add_section_btn)
        
        # Items list
        items_label = QLabel("الأصناف:")
        items_label.setFont(QFont("Arial", 14, QFont.Bold))
        items_label.setStyleSheet("color: #1E2A38;")
        menu_layout.addWidget(items_label)
        
        self.items_list = QListWidget()
        self.items_list.setStyleSheet("""
            QListWidget {
                border: 2px solid #ddd;
                border-radius: 8px;
                background-color: white;
            }
            QListWidget::item {
                padding: 10px;
                border-bottom: 1px solid #eee;
            }
            QListWidget::item:selected {
                background-color: #1E2A38;
                color: white;
            }
        """)
        self.items_list.itemClicked.connect(self.select_item)
        menu_layout.addWidget(self.items_list)
        
        # Add item button
        add_item_btn = QPushButton("➕ إضافة صنف")
        add_item_btn.setFont(QFont("Arial", 10))
        add_item_btn.setStyleSheet("""
            QPushButton {
                background-color: #D4AF37;
                color: white;
                border: none;
                border-radius: 5px;
                padding: 5px 10px;
            }
            QPushButton:hover {
                background-color: #B8941F;
            }
        """)
        add_item_btn.clicked.connect(self.add_item)
        menu_layout.addWidget(add_item_btn)
        
        parent_layout.addWidget(menu_widget)
    
    def create_number_pad(self, parent_layout):
        """Create number pad for quantity"""
        number_widget = QWidget()
        number_widget.setMinimumWidth(180)
        number_widget.setMaximumWidth(220)
        number_layout = QVBoxLayout(number_widget)
        number_layout.setContentsMargins(10, 10, 10, 10)
        number_layout.setSpacing(10)
        
        # Quantity label
        qty_label = QLabel("الكمية:")
        qty_label.setFont(QFont("Arial", 14, QFont.Bold))
        qty_label.setStyleSheet("color: #1E2A38;")
        qty_label.setAlignment(Qt.AlignCenter)
        number_layout.addWidget(qty_label)
        
        # Quantity display (now supports fractions)
        self.quantity_display = QLineEdit()
        self.quantity_display.setText("1")
        self.quantity_display.setReadOnly(True)
        self.quantity_display.setFont(QFont("Arial", 16, QFont.Bold))
        self.quantity_display.setFixedHeight(50)
        self.quantity_display.setAlignment(Qt.AlignCenter)
        self.quantity_display.setStyleSheet("""
            QLineEdit {
                border: 2px solid #1E2A38;
                border-radius: 8px;
                padding: 5px;
                text-align: center;
                background-color: #f8f9fa;
            }
        """)
        self.current_quantity = 1.0  # Store actual quantity value
        number_layout.addWidget(self.quantity_display)
        
        # Number pad buttons
        number_grid = QGridLayout()
        number_grid.setSpacing(5)
        
        # Create number buttons 1-9
        for i in range(1, 10):
            row = (i - 1) // 3
            col = (i - 1) % 3
            btn = QPushButton(str(i))
            btn.setFixedSize(50, 50)
            btn.setFont(QFont("Arial", 14, QFont.Bold))
            btn.setStyleSheet("""
                QPushButton {
                    background-color: #1E2A38;
                    color: white;
                    border: none;
                    border-radius: 8px;
                }
                QPushButton:hover {
                    background-color: #D4AF37;
                }
            """)
            btn.clicked.connect(lambda checked, num=i: self.set_quantity(num))
            number_grid.addWidget(btn, row, col)
        
        # Half button (نصف)
        half_btn = QPushButton("½")
        half_btn.setFixedSize(50, 50)
        half_btn.setFont(QFont("Arial", 16, QFont.Bold))
        half_btn.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                border: none;
                border-radius: 8px;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        half_btn.clicked.connect(lambda: self.set_fraction(0.5))
        number_grid.addWidget(half_btn, 3, 0)

        # Zero button
        zero_btn = QPushButton("0")
        zero_btn.setFixedSize(50, 50)
        zero_btn.setFont(QFont("Arial", 14, QFont.Bold))
        zero_btn.setStyleSheet("""
            QPushButton {
                background-color: #1E2A38;
                color: white;
                border: none;
                border-radius: 8px;
            }
            QPushButton:hover {
                background-color: #D4AF37;
            }
        """)
        zero_btn.clicked.connect(lambda: self.set_quantity(10))
        number_grid.addWidget(zero_btn, 3, 1)

        # Quarter button (ربع)
        quarter_btn = QPushButton("¼")
        quarter_btn.setFixedSize(50, 50)
        quarter_btn.setFont(QFont("Arial", 16, QFont.Bold))
        quarter_btn.setStyleSheet("""
            QPushButton {
                background-color: #ffc107;
                color: #212529;
                border: none;
                border-radius: 8px;
            }
            QPushButton:hover {
                background-color: #e0a800;
            }
        """)
        quarter_btn.clicked.connect(lambda: self.set_fraction(0.25))
        number_grid.addWidget(quarter_btn, 3, 2)

        # Clear button
        clear_btn = QPushButton("C")
        clear_btn.setFixedSize(50, 50)
        clear_btn.setFont(QFont("Arial", 14, QFont.Bold))
        clear_btn.setStyleSheet("""
            QPushButton {
                background-color: #dc3545;
                color: white;
                border: none;
                border-radius: 8px;
            }
            QPushButton:hover {
                background-color: #c82333;
            }
        """)
        clear_btn.clicked.connect(self.clear_quantity)
        number_grid.addWidget(clear_btn, 4, 1)
        
        number_layout.addLayout(number_grid)
        number_layout.addStretch()
        
        parent_layout.addWidget(number_widget)

    def create_order_summary(self, parent_layout):
        """Create order summary area"""
        order_widget = QWidget()
        order_widget.setMinimumWidth(400)
        order_widget.setMaximumWidth(500)
        order_layout = QVBoxLayout(order_widget)
        order_layout.setContentsMargins(15, 15, 15, 15)
        order_layout.setSpacing(15)

        # Order summary label
        summary_label = QLabel("ملخص الطلب:")
        summary_label.setFont(QFont("Arial", 14, QFont.Bold))
        summary_label.setStyleSheet("color: #1E2A38;")
        order_layout.addWidget(summary_label)

        # Order items list
        self.order_list = QListWidget()
        self.order_list.setMinimumHeight(300)
        self.order_list.setStyleSheet("""
            QListWidget {
                border: 2px solid #ddd;
                border-radius: 8px;
                background-color: white;
                font-size: 13px;
            }
            QListWidget::item {
                padding: 12px;
                border-bottom: 1px solid #eee;
                min-height: 40px;
            }
            QListWidget::item:selected {
                background-color: #e3f2fd;
            }
        """)
        order_layout.addWidget(self.order_list)

        # Total amount
        self.total_label = QLabel("المجموع: 0.00 دينار")
        self.total_label.setFont(QFont("Arial", 16, QFont.Bold))
        self.total_label.setStyleSheet("color: #1E2A38; padding: 10px; background-color: #f8f9fa; border-radius: 5px;")
        self.total_label.setAlignment(Qt.AlignCenter)
        order_layout.addWidget(self.total_label)

        # Notes
        notes_label = QLabel("ملاحظات:")
        notes_label.setFont(QFont("Arial", 12, QFont.Bold))
        notes_label.setStyleSheet("color: #1E2A38;")
        order_layout.addWidget(notes_label)

        self.notes_text = QTextEdit()
        self.notes_text.setFixedHeight(60)
        self.notes_text.setPlaceholderText("أضف ملاحظات للطلب...")
        self.notes_text.setStyleSheet("""
            QTextEdit {
                border: 2px solid #ddd;
                border-radius: 5px;
                padding: 5px;
            }
        """)
        order_layout.addWidget(self.notes_text)

        # Action buttons
        buttons_layout = QVBoxLayout()
        buttons_layout.setSpacing(15)

        # Print buttons (horizontal layout)
        print_layout = QHBoxLayout()
        print_layout.setSpacing(10)

        # Cashier print button
        print_cashier_btn = QPushButton("💰")
        print_cashier_btn.setFixedSize(60, 50)
        print_cashier_btn.setFont(QFont("Arial", 16))
        print_cashier_btn.setToolTip("طباعة كاشير")
        print_cashier_btn.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                border: none;
                border-radius: 8px;
                font-size: 18px;
            }
            QPushButton:hover {
                background-color: #218838;
                transform: scale(1.1);
            }
        """)
        print_cashier_btn.clicked.connect(lambda: self.create_and_print_order('cashier'))
        print_layout.addWidget(print_cashier_btn)

        # Kitchen print button
        print_kitchen_btn = QPushButton("🍳")
        print_kitchen_btn.setFixedSize(60, 50)
        print_kitchen_btn.setFont(QFont("Arial", 16))
        print_kitchen_btn.setToolTip("طباعة مطبخ")
        print_kitchen_btn.setStyleSheet("""
            QPushButton {
                background-color: #007bff;
                color: white;
                border: none;
                border-radius: 8px;
                font-size: 18px;
            }
            QPushButton:hover {
                background-color: #0056b3;
                transform: scale(1.1);
            }
        """)
        print_kitchen_btn.clicked.connect(lambda: self.create_and_print_order('kitchen'))
        print_layout.addWidget(print_kitchen_btn)

        # Grill print button
        print_grill_btn = QPushButton("🔥")
        print_grill_btn.setFixedSize(60, 50)
        print_grill_btn.setFont(QFont("Arial", 16))
        print_grill_btn.setToolTip("طباعة مشاوي")
        print_grill_btn.setStyleSheet("""
            QPushButton {
                background-color: #fd7e14;
                color: white;
                border: none;
                border-radius: 8px;
                font-size: 18px;
            }
            QPushButton:hover {
                background-color: #e55a00;
                transform: scale(1.1);
            }
        """)
        print_grill_btn.clicked.connect(lambda: self.create_and_print_order('grill'))
        print_layout.addWidget(print_grill_btn)

        # Printer check button
        check_printers_btn = QPushButton("🖨️")
        check_printers_btn.setFixedSize(60, 50)
        check_printers_btn.setFont(QFont("Arial", 16))
        check_printers_btn.setToolTip("فحص الطابعات")
        check_printers_btn.setStyleSheet("""
            QPushButton {
                background-color: #6c757d;
                color: white;
                border: none;
                border-radius: 8px;
                font-size: 18px;
            }
            QPushButton:hover {
                background-color: #545b62;
                transform: scale(1.1);
            }
        """)
        check_printers_btn.clicked.connect(self.check_printers)
        print_layout.addWidget(check_printers_btn)

        buttons_layout.addLayout(print_layout)

        # Cancel and clear buttons
        cancel_btn = QPushButton("❌ الغاء الطلب")
        cancel_btn.setFont(QFont("Arial", 12, QFont.Bold))
        cancel_btn.setFixedHeight(40)
        cancel_btn.setStyleSheet("""
            QPushButton {
                background-color: #dc3545;
                color: white;
                border: none;
                border-radius: 8px;
            }
            QPushButton:hover {
                background-color: #c82333;
            }
        """)
        cancel_btn.clicked.connect(self.cancel_order)
        buttons_layout.addWidget(cancel_btn)

        clear_btn = QPushButton("♻️ تنظيف")
        clear_btn.setFont(QFont("Arial", 12, QFont.Bold))
        clear_btn.setFixedHeight(40)
        clear_btn.setStyleSheet("""
            QPushButton {
                background-color: #6c757d;
                color: white;
                border: none;
                border-radius: 8px;
            }
            QPushButton:hover {
                background-color: #545b62;
            }
        """)
        clear_btn.clicked.connect(self.clear_order)
        buttons_layout.addWidget(clear_btn)

        order_layout.addLayout(buttons_layout)
        parent_layout.addWidget(order_widget)

    def create_footer_navigation(self, parent_layout):
        """Create footer navigation bar"""
        footer = QFrame()
        footer.setFixedHeight(70)
        footer.setStyleSheet("""
            QFrame {
                background-color: #1E2A38;
                border-top: 2px solid #D4AF37;
            }
        """)

        footer_layout = QHBoxLayout(footer)
        footer_layout.setContentsMargins(20, 10, 20, 10)
        footer_layout.setSpacing(20)

        # Back button
        back_btn = QPushButton("🔙 العودة")
        back_btn.setFont(QFont("Arial", 12, QFont.Bold))
        back_btn.setStyleSheet("""
            QPushButton {
                background-color: transparent;
                color: #D4AF37;
                border: 2px solid #D4AF37;
                border-radius: 8px;
                padding: 8px 16px;
            }
            QPushButton:hover {
                background-color: #D4AF37;
                color: #1E2A38;
            }
        """)
        back_btn.clicked.connect(self.go_back)
        footer_layout.addWidget(back_btn)

        # Spacer
        footer_layout.addStretch()

        # Old invoices button
        invoices_btn = QPushButton("📜 عرض الفواتير القديمة")
        invoices_btn.setFont(QFont("Arial", 12, QFont.Bold))
        invoices_btn.setStyleSheet("""
            QPushButton {
                background-color: transparent;
                color: #D4AF37;
                border: 2px solid #D4AF37;
                border-radius: 8px;
                padding: 8px 16px;
            }
            QPushButton:hover {
                background-color: #D4AF37;
                color: #1E2A38;
            }
        """)
        invoices_btn.clicked.connect(self.main_app.show_invoices)
        footer_layout.addWidget(invoices_btn)

        # Sales summary button
        sales_btn = QPushButton("📊 مبيعات اليوم")
        sales_btn.setFont(QFont("Arial", 12, QFont.Bold))
        sales_btn.setStyleSheet("""
            QPushButton {
                background-color: transparent;
                color: #D4AF37;
                border: 2px solid #D4AF37;
                border-radius: 8px;
                padding: 8px 16px;
            }
            QPushButton:hover {
                background-color: #D4AF37;
                color: #1E2A38;
            }
        """)
        sales_btn.clicked.connect(self.main_app.show_sales)
        footer_layout.addWidget(sales_btn)

        parent_layout.addWidget(footer)

    def set_order_context(self, order_type, hall_id=None, table_number=None):
        """Set order context (dine-in, takeaway, delivery)"""
        self.order_type = order_type
        self.hall_id = hall_id
        self.table_number = table_number

        # Update order info label
        if order_type == 'dine-in':
            self.order_info_label.setText(f"طاولة {table_number}")
        elif order_type == 'takeaway':
            self.order_info_label.setText("طلب سفري")
        elif order_type == 'delivery':
            self.order_info_label.setText("طلب دلفري")

    def refresh_data(self):
        """Refresh menu data"""
        self.load_thread = MenuLoadThread(self.main_app.api_client)
        self.load_thread.data_loaded.connect(self.handle_menu_loaded)
        self.load_thread.start()

    def handle_menu_loaded(self, result):
        """Handle loaded menu data"""
        if 'error' in result:
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل القائمة: {result['error']}")
            return

        if 'sections' in result:
            self.menu_data = result['sections']
            self.update_sections_list()

    def update_sections_list(self):
        """Update sections list"""
        self.sections_list.clear()
        for section in self.menu_data:
            item = QListWidgetItem(section['name'])
            item.setData(Qt.UserRole, section)
            self.sections_list.addItem(item)

        # Select first section by default
        if self.menu_data:
            self.sections_list.setCurrentRow(0)
            self.select_section(self.sections_list.item(0))

    def select_section(self, item):
        """Select menu section"""
        if not item:
            return

        section = item.data(Qt.UserRole)
        self.current_section = section
        self.update_items_list()

    def update_items_list(self):
        """Update items list for selected section"""
        self.items_list.clear()

        if not self.current_section:
            return

        for item in self.current_section.get('items', []):
            list_item = QListWidgetItem(f"{item['name']} - {item['price']:.2f} دينار")
            list_item.setData(Qt.UserRole, item)
            self.items_list.addItem(list_item)

    def select_item(self, list_item):
        """Select menu item and add to order"""
        if not list_item:
            return

        item = list_item.data(Qt.UserRole)
        quantity = self.current_quantity

        # Check if item already exists in order
        existing_item = None
        for order_item in self.order_items:
            if order_item['item']['id'] == item['id']:
                existing_item = order_item
                break

        if existing_item:
            existing_item['quantity'] += quantity
        else:
            self.order_items.append({
                'item': item,
                'quantity': quantity
            })

        self.update_order_summary()
        self.clear_quantity()

    def update_order_summary(self):
        """Update order summary display"""
        self.order_list.clear()
        total_amount = 0

        for i, order_item in enumerate(self.order_items):
            item = order_item['item']
            quantity = order_item['quantity']
            item_total = item['price'] * quantity
            total_amount += item_total

            # Create custom widget for order item
            item_widget = QWidget()
            item_layout = QHBoxLayout(item_widget)
            item_layout.setContentsMargins(5, 5, 5, 5)
            item_layout.setSpacing(10)

            # Item info
            if quantity == 0.5:
                qty_text = "½"
            elif quantity == 0.25:
                qty_text = "¼"
            elif quantity == int(quantity):
                qty_text = str(int(quantity))
            else:
                qty_text = f"{quantity:.2f}"

            item_info = QLabel(f"{item['name']}\n{qty_text} × {item['price']:.2f} = {item_total:.2f} دينار")
            item_info.setFont(QFont("Arial", 11))
            item_info.setStyleSheet("color: #333; padding: 5px;")
            item_layout.addWidget(item_info)

            # Delete button
            delete_btn = QPushButton("🗑️")
            delete_btn.setFixedSize(30, 30)
            delete_btn.setFont(QFont("Arial", 12))
            delete_btn.setStyleSheet("""
                QPushButton {
                    background-color: #dc3545;
                    color: white;
                    border: none;
                    border-radius: 15px;
                    font-size: 14px;
                }
                QPushButton:hover {
                    background-color: #c82333;
                    transform: scale(1.1);
                }
            """)
            delete_btn.clicked.connect(lambda checked, idx=i: self.remove_order_item(idx))
            item_layout.addWidget(delete_btn)

            # Add to list
            list_item = QListWidgetItem()
            list_item.setSizeHint(item_widget.sizeHint())
            self.order_list.addItem(list_item)
            self.order_list.setItemWidget(list_item, item_widget)

        self.total_label.setText(f"المجموع: {total_amount:.2f} دينار")

    def set_quantity(self, number):
        """Set quantity from number pad"""
        if number == 10:  # Zero button
            if self.current_quantity < 10:
                self.current_quantity = self.current_quantity * 10
        else:
            if self.current_quantity == 1:
                self.current_quantity = number
            else:
                new_value = self.current_quantity * 10 + number
                if new_value <= 99:
                    self.current_quantity = new_value

        self.update_quantity_display()

    def set_fraction(self, fraction):
        """Set fractional quantity (0.5 for half, 0.25 for quarter)"""
        self.current_quantity = fraction
        self.update_quantity_display()

    def clear_quantity(self):
        """Clear quantity to 1"""
        self.current_quantity = 1.0
        self.update_quantity_display()

    def update_quantity_display(self):
        """Update quantity display text"""
        if self.current_quantity == 0.5:
            self.quantity_display.setText("½")
        elif self.current_quantity == 0.25:
            self.quantity_display.setText("¼")
        elif self.current_quantity == int(self.current_quantity):
            self.quantity_display.setText(str(int(self.current_quantity)))
        else:
            self.quantity_display.setText(f"{self.current_quantity:.2f}")

    def create_and_print_order(self, printer_type):
        """Create order and print"""
        if not self.order_items:
            QMessageBox.warning(self, "تحذير", "لا توجد أصناف في الطلب")
            return

        # Prepare order data
        order_data = {
            'type': self.order_type,
            'items': [
                {
                    'id': order_item['item']['id'],
                    'name': order_item['item']['name'],
                    'price': order_item['item']['price'],
                    'quantity': order_item['quantity']
                }
                for order_item in self.order_items
            ],
            'notes': self.notes_text.toPlainText().strip()
        }

        if self.order_type == 'dine-in':
            order_data['hallId'] = self.hall_id
            order_data['tableNumber'] = self.table_number
        elif self.order_type == 'delivery':
            order_data['customerPhone'] = getattr(self.main_app, 'delivery_phone', '')
            order_data['customerAddress'] = getattr(self.main_app, 'delivery_address', '')

        # Create order
        self.create_thread = OrderCreateThread(self.main_app.api_client, order_data)
        self.create_thread.order_created.connect(lambda result: self.handle_order_created(result, printer_type))
        self.create_thread.start()

    def handle_order_created(self, result, printer_type):
        """Handle order creation result"""
        if 'error' in result:
            QMessageBox.critical(self, "خطأ", f"فشل في إنشاء الطلب: {result['error']}")
            return

        if 'order' in result:
            order_id = result['order']['id']

            # Print based on type
            if printer_type == 'kitchen':
                print_result = self.main_app.api_client.print_kitchen_order(order_id)
            else:
                print_result = self.main_app.api_client.print_receipt(order_id, printer_type)

            if 'error' in print_result:
                QMessageBox.warning(self, "تحذير", f"تم إنشاء الطلب ولكن فشلت الطباعة: {print_result['error']}")
            else:
                QMessageBox.information(self, "نجح", "تم إنشاء الطلب وطباعته بنجاح")

            self.clear_order()

    def cancel_order(self):
        """Cancel current order"""
        if self.order_items:
            reply = QMessageBox.question(self, "تأكيد", "هل تريد إلغاء الطلب الحالي؟",
                                       QMessageBox.Yes | QMessageBox.No)
            if reply == QMessageBox.Yes:
                self.clear_order()

    def clear_order(self):
        """Clear current order"""
        self.order_items.clear()
        self.notes_text.clear()
        self.update_order_summary()

    def go_back(self):
        """Go back to previous screen"""
        if self.order_type == 'dine-in':
            self.main_app.show_tables()
        else:
            self.main_app.show_dashboard()

    def add_section(self):
        """Add new menu section"""
        section_name, ok = QInputDialog.getText(self, 'إضافة قسم جديد', 'اسم القسم:')
        if not ok or not section_name.strip():
            return

        result = self.main_app.api_client.create_menu_section(section_name.strip())

        if 'error' in result:
            QMessageBox.critical(self, "خطأ", f"فشل في إضافة القسم: {result['error']}")
        else:
            QMessageBox.information(self, "نجح", "تم إضافة القسم بنجاح")
            self.refresh_data()

    def add_item(self):
        """Add new menu item"""
        if not self.current_section:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار قسم أولاً")
            return

        item_name, ok = QInputDialog.getText(self, 'إضافة صنف جديد', 'اسم الصنف:')
        if not ok or not item_name.strip():
            return

        price, ok = QInputDialog.getDouble(self, 'إضافة صنف جديد', 'السعر:', 0.0, 0.0, 999.99, 2)
        if not ok:
            return

        result = self.main_app.api_client.add_menu_item(
            self.current_section['id'],
            item_name.strip(),
            price
        )

        if 'error' in result:
            QMessageBox.critical(self, "خطأ", f"فشل في إضافة الصنف: {result['error']}")
        else:
            QMessageBox.information(self, "نجح", "تم إضافة الصنف بنجاح")
            self.refresh_data()

    def remove_order_item(self, index):
        """Remove item from order"""
        if 0 <= index < len(self.order_items):
            self.order_items.pop(index)
            self.update_order_summary()

    def check_printers(self):
        """Check available printers"""
        try:
            result = self.main_app.api_client.get_printers()
            if 'error' in result:
                QMessageBox.critical(self, "خطأ", f"فشل في فحص الطابعات: {result['error']}")
                return

            available = result.get('availablePrinters', [])
            assigned = result.get('assignedPrinters', {})

            message = "الطابعات المتاحة:\n\n"
            for printer in available:
                message += f"• {printer['name']}\n"

            message += "\nالطابعات المعينة:\n"
            message += f"• كاشير: {assigned.get('cashier', 'غير معين')}\n"
            message += f"• مطبخ: {assigned.get('kitchen', 'غير معين')}\n"
            message += f"• مشاوي: {assigned.get('grill', 'غير معين')}\n"

            QMessageBox.information(self, "حالة الطابعات", message)

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في فحص الطابعات: {str(e)}")

    def show_settings(self):
        """Show settings dialog"""
        from gui.settings import SettingsDialog
        settings_dialog = SettingsDialog(self.main_app)
        settings_dialog.exec_()
