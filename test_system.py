#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Test script for Restaurant Billing System
Tests the API endpoints and basic functionality
"""

import requests
import json
import time

BASE_URL = "http://localhost:3000/api"

def test_api():
    """Test the API endpoints"""
    print("🧪 Testing Restaurant Billing System API...")
    
    # Test health check
    print("\n1. Testing health check...")
    try:
        response = requests.get(f"{BASE_URL}/health")
        if response.status_code == 200:
            print("✅ Health check passed")
            print(f"   Response: {response.json()}")
        else:
            print("❌ Health check failed")
            return False
    except Exception as e:
        print(f"❌ Health check error: {e}")
        return False
    
    # Test login
    print("\n2. Testing login...")
    try:
        login_data = {"username": "admin", "password": "password"}
        response = requests.post(f"{BASE_URL}/auth/login", json=login_data)
        if response.status_code == 200:
            print("✅ Login successful")
            data = response.json()
            token = data.get('token')
            user = data.get('user')
            print(f"   User: {user.get('name')} ({user.get('role')})")
        else:
            print("❌ Login failed")
            return False
    except Exception as e:
        print(f"❌ Login error: {e}")
        return False
    
    # Set up headers for authenticated requests
    headers = {"Authorization": f"Bearer {token}"}
    
    # Test halls
    print("\n3. Testing halls...")
    try:
        response = requests.get(f"{BASE_URL}/halls", headers=headers)
        if response.status_code == 200:
            print("✅ Halls retrieved successfully")
            halls = response.json().get('halls', [])
            print(f"   Found {len(halls)} halls")
            for hall in halls:
                print(f"   - {hall.get('name')}: {len(hall.get('tables', []))} tables")
        else:
            print("❌ Failed to retrieve halls")
    except Exception as e:
        print(f"❌ Halls error: {e}")
    
    # Test menu
    print("\n4. Testing menu...")
    try:
        response = requests.get(f"{BASE_URL}/menu", headers=headers)
        if response.status_code == 200:
            print("✅ Menu retrieved successfully")
            sections = response.json().get('sections', [])
            print(f"   Found {len(sections)} sections")
            for section in sections:
                items = section.get('items', [])
                print(f"   - {section.get('name')}: {len(items)} items")
                for item in items[:3]:  # Show first 3 items
                    print(f"     * {item.get('name')}: {item.get('price')} دينار")
        else:
            print("❌ Failed to retrieve menu")
    except Exception as e:
        print(f"❌ Menu error: {e}")
    
    # Test creating an order
    print("\n5. Testing order creation...")
    try:
        order_data = {
            "type": "takeaway",
            "items": [
                {"id": 1, "name": "كباب لحم", "price": 8.5, "quantity": 2},
                {"id": 4, "name": "شاي", "price": 1.0, "quantity": 1}
            ],
            "notes": "اختبار النظام"
        }
        response = requests.post(f"{BASE_URL}/orders", json=order_data, headers=headers)
        if response.status_code == 201:
            print("✅ Order created successfully")
            order = response.json().get('order')
            print(f"   Order #{order.get('orderNumber')}: {order.get('totalAmount')} دينار")
            order_id = order.get('id')
        else:
            print("❌ Failed to create order")
            print(f"   Response: {response.text}")
    except Exception as e:
        print(f"❌ Order creation error: {e}")
    
    # Test retrieving orders
    print("\n6. Testing order retrieval...")
    try:
        response = requests.get(f"{BASE_URL}/orders?date=today", headers=headers)
        if response.status_code == 200:
            print("✅ Orders retrieved successfully")
            orders = response.json().get('orders', [])
            print(f"   Found {len(orders)} orders today")
            total_sales = sum(order.get('totalAmount', 0) for order in orders)
            print(f"   Total sales: {total_sales:.2f} دينار")
        else:
            print("❌ Failed to retrieve orders")
    except Exception as e:
        print(f"❌ Order retrieval error: {e}")
    
    # Test printers
    print("\n7. Testing printers...")
    try:
        response = requests.get(f"{BASE_URL}/printers", headers=headers)
        if response.status_code == 200:
            print("✅ Printers retrieved successfully")
            data = response.json()
            available = data.get('availablePrinters', [])
            assigned = data.get('assignedPrinters', {})
            print(f"   Available printers: {len(available)}")
            print(f"   Assigned printers: {assigned}")
        else:
            print("❌ Failed to retrieve printers")
    except Exception as e:
        print(f"❌ Printers error: {e}")
    
    print("\n🎉 API testing completed!")
    return True

def test_data_files():
    """Test that data files are created and contain valid JSON"""
    print("\n📁 Testing data files...")
    
    data_files = [
        'data/users.json',
        'data/halls.json', 
        'data/menu.json',
        'data/orders.json',
        'data/settings.json'
    ]
    
    for file_path in data_files:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
                print(f"✅ {file_path}: Valid JSON")
        except FileNotFoundError:
            print(f"❌ {file_path}: File not found")
        except json.JSONDecodeError:
            print(f"❌ {file_path}: Invalid JSON")
        except Exception as e:
            print(f"❌ {file_path}: Error - {e}")

def main():
    """Main test function"""
    print("🚀 Restaurant Billing System - End-to-End Test")
    print("=" * 50)
    
    # Test data files
    test_data_files()
    
    # Wait a moment for server to be ready
    print("\n⏳ Waiting for server to be ready...")
    time.sleep(2)
    
    # Test API
    success = test_api()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 All tests passed! System is working correctly.")
        print("\n📋 Next steps:")
        print("1. Backend server is running on http://localhost:3000")
        print("2. Python GUI application should be running")
        print("3. Login with: admin / password")
        print("4. Test the GUI features:")
        print("   - Tables management")
        print("   - Menu ordering")
        print("   - Takeaway orders")
        print("   - Delivery orders")
        print("   - Invoice printing")
        print("   - Sales reports")
    else:
        print("❌ Some tests failed. Please check the server and try again.")

if __name__ == "__main__":
    main()
