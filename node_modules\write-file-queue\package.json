{"name": "write-file-queue", "version": "0.0.1", "description": "A writeFile queue which reattempts to write after errors occur", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "https://github.com/wankdanker/node-write-file-queue.git"}, "keywords": ["write", "file", "queue", "rewrite", "reattempt", "error"], "author": "<PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/wankdanker/node-write-file-queue/issues"}, "homepage": "https://github.com/wankdanker/node-write-file-queue", "dependencies": {"dank-do-while": "^0.1.2"}}