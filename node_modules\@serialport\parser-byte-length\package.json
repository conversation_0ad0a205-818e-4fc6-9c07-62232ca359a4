{"name": "@serialport/parser-byte-length", "version": "12.0.0", "main": "./dist/index.js", "types": "./dist/index.d.ts", "engines": {"node": ">=12.0.0"}, "publishConfig": {"access": "public"}, "license": "MIT", "scripts": {"build": "tsc --build tsconfig-build.json"}, "repository": {"type": "git", "url": "git://github.com/serialport/node-serialport.git"}, "funding": "https://opencollective.com/serialport/donate", "devDependencies": {"typescript": "5.2.2"}, "gitHead": "f7e7bd53f9578a26c4f44cc1949fef396dc064c7"}