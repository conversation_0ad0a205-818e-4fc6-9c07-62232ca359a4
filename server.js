const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const cors = require('cors');
const path = require('path');
const fs = require('fs');

// Import routes
const authRoutes = require('./routes/auth');
const hallRoutes = require('./routes/halls');
const menuRoutes = require('./routes/menu');
const orderRoutes = require('./routes/orders');
const printerRoutes = require('./routes/printers');

const app = express();
const server = http.createServer(app);
const io = socketIo(server, {
    cors: {
        origin: "*",
        methods: ["GET", "POST"]
    }
});
const PORT = process.env.PORT || 3002;

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Create data directory if it doesn't exist
const dataDir = path.join(__dirname, 'data');
if (!fs.existsSync(dataDir)) {
    fs.mkdirSync(dataDir, { recursive: true });
}

// Initialize JSON files if they don't exist
const initializeDataFiles = () => {
    const files = {
        'users.json': {
            users: [
                {
                    id: 1,
                    username: 'admin',
                    password: '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', // password
                    role: 'admin',
                    name: 'Administrator'
                },
                {
                    id: 2,
                    username: 'waiter1',
                    password: '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', // password
                    role: 'waiter',
                    name: 'Waiter 1'
                }
            ]
        },
        'halls.json': {
            halls: [
                {
                    id: 1,
                    name: 'الصالة الرئيسية',
                    tables: [
                        { id: 1, number: 1, status: 'available' },
                        { id: 2, number: 2, status: 'available' },
                        { id: 3, number: 3, status: 'available' },
                        { id: 4, number: 4, status: 'available' }
                    ]
                }
            ]
        },
        'menu.json': {
            sections: [
                {
                    id: 1,
                    name: 'مشاوي',
                    items: [
                        { id: 1, name: 'كباب لحم', price: 8.5 },
                        { id: 2, name: 'كباب دجاج', price: 7.0 },
                        { id: 3, name: 'تكة لحم', price: 9.0 }
                    ]
                },
                {
                    id: 2,
                    name: 'مشروبات',
                    items: [
                        { id: 4, name: 'شاي', price: 1.0 },
                        { id: 5, name: 'قهوة', price: 1.5 },
                        { id: 6, name: 'عصير برتقال', price: 2.0 }
                    ]
                }
            ]
        },
        'orders.json': {
            orders: []
        },
        'settings.json': {
            restaurant: {
                name: 'مطعم النوفوس',
                phone: '+964 123 456 789',
                address: 'بغداد، العراق'
            },
            printers: {
                cashier: null,
                kitchen: null,
                grill: null
            }
        }
    };

    Object.keys(files).forEach(filename => {
        const filePath = path.join(dataDir, filename);
        if (!fs.existsSync(filePath)) {
            fs.writeFileSync(filePath, JSON.stringify(files[filename], null, 2));
            console.log(`Created ${filename}`);
        }
    });
};

// Initialize data files
initializeDataFiles();

// Socket.io connection handling
io.on('connection', (socket) => {
    console.log('Client connected:', socket.id);

    socket.on('join_cashier', () => {
        socket.join('cashiers');
        console.log('Cashier joined:', socket.id);
    });

    socket.on('disconnect', () => {
        console.log('Client disconnected:', socket.id);
    });
});

// Make io available to routes
app.set('io', io);

// Routes
app.use('/api/auth', authRoutes);
app.use('/api/halls', hallRoutes);
app.use('/api/menu', menuRoutes);
app.use('/api/orders', orderRoutes);
app.use('/api/printers', printerRoutes);

// Health check endpoint
app.get('/api/health', (req, res) => {
    res.json({ status: 'OK', message: 'Restaurant Billing System API is running' });
});

// Error handling middleware
app.use((err, req, res, next) => {
    console.error(err.stack);
    res.status(500).json({ error: 'Something went wrong!' });
});

// 404 handler
app.use('*', (req, res) => {
    res.status(404).json({ error: 'Route not found' });
});

server.listen(PORT, () => {
    console.log(`🚀 Restaurant Billing System Backend running on port ${PORT}`);
    console.log(`📊 API Health Check: http://localhost:${PORT}/api/health`);
    console.log(`🔌 Socket.io enabled for real-time notifications`);
});

module.exports = app;
