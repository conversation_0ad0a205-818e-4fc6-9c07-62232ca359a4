from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *
import webbrowser

class AboutDialog(QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("حول البرنامج")
        self.setFixedSize(500, 600)
        self.setModal(True)
        self.setup_ui()
    
    def setup_ui(self):
        """Setup the about dialog UI"""
        layout = QVBoxLayout(self)
        layout.setSpacing(20)
        layout.setContentsMargins(30, 30, 30, 30)
        
        # Header with logo area
        header_frame = QFrame()
        header_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #1E2A38, stop:1 #D4AF37);
                border-radius: 15px;
                padding: 20px;
            }
        """)
        header_layout = QVBoxLayout(header_frame)
        
        # App icon/logo
        logo_label = QLabel("🍽️")
        logo_label.setAlignment(Qt.AlignCenter)
        logo_label.setFont(QFont("Arial", 48))
        logo_label.setStyleSheet("color: white; background: transparent;")
        header_layout.addWidget(logo_label)
        
        # App name
        app_name = QLabel("نظام إدارة مطعم النوفوس")
        app_name.setAlignment(Qt.AlignCenter)
        app_name.setFont(QFont("Arial", 18, QFont.Bold))
        app_name.setStyleSheet("color: white; background: transparent;")
        header_layout.addWidget(app_name)
        
        # Version
        version_label = QLabel("الإصدار 1.0.0")
        version_label.setAlignment(Qt.AlignCenter)
        version_label.setFont(QFont("Arial", 12))
        version_label.setStyleSheet("color: white; background: transparent;")
        header_layout.addWidget(version_label)
        
        layout.addWidget(header_frame)
        
        # Description
        desc_frame = QFrame()
        desc_frame.setStyleSheet("""
            QFrame {
                background-color: #f8f9fa;
                border-radius: 10px;
                padding: 15px;
                border: 1px solid #dee2e6;
            }
        """)
        desc_layout = QVBoxLayout(desc_frame)
        
        description = QLabel("""
نظام شامل لإدارة المطاعم يتضمن:
• إدارة الطاولات والطلبات
• نظام النوادل المتقدم
• إدارة القوائم والأصناف
• نظام الطباعة الحراري
• تقارير المبيعات
• واجهة سهلة الاستخدام
        """)
        description.setFont(QFont("Arial", 11))
        description.setStyleSheet("color: #495057; line-height: 1.6;")
        description.setWordWrap(True)
        desc_layout.addWidget(description)
        
        layout.addWidget(desc_frame)
        
        # Company info
        company_frame = QFrame()
        company_frame.setStyleSheet("""
            QFrame {
                background-color: #1E2A38;
                border-radius: 10px;
                padding: 20px;
            }
        """)
        company_layout = QVBoxLayout(company_frame)
        
        # Company name
        company_name = QLabel("شركة الباش البرمجية")
        company_name.setAlignment(Qt.AlignCenter)
        company_name.setFont(QFont("Arial", 16, QFont.Bold))
        company_name.setStyleSheet("color: #D4AF37; margin-bottom: 10px;")
        company_layout.addWidget(company_name)
        
        # Contact info
        contact_layout = QVBoxLayout()
        contact_layout.setSpacing(8)
        
        # Phone
        phone_layout = QHBoxLayout()
        phone_icon = QLabel("📱")
        phone_icon.setFont(QFont("Arial", 14))
        phone_icon.setStyleSheet("color: #D4AF37;")
        phone_text = QLabel("الهاتف: 07715086335")
        phone_text.setFont(QFont("Arial", 12))
        phone_text.setStyleSheet("color: white;")
        phone_layout.addWidget(phone_icon)
        phone_layout.addWidget(phone_text)
        phone_layout.addStretch()
        contact_layout.addLayout(phone_layout)
        
        # WhatsApp
        whatsapp_layout = QHBoxLayout()
        whatsapp_icon = QLabel("💬")
        whatsapp_icon.setFont(QFont("Arial", 14))
        whatsapp_icon.setStyleSheet("color: #D4AF37;")
        whatsapp_text = QLabel("واتساب: 07715086335")
        whatsapp_text.setFont(QFont("Arial", 12))
        whatsapp_text.setStyleSheet("color: white;")
        whatsapp_btn = QPushButton("فتح واتساب")
        whatsapp_btn.setFixedSize(100, 30)
        whatsapp_btn.setStyleSheet("""
            QPushButton {
                background-color: #25D366;
                color: white;
                border: none;
                border-radius: 5px;
                font-size: 10px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #128C7E;
            }
        """)
        whatsapp_btn.clicked.connect(self.open_whatsapp)
        whatsapp_layout.addWidget(whatsapp_icon)
        whatsapp_layout.addWidget(whatsapp_text)
        whatsapp_layout.addStretch()
        whatsapp_layout.addWidget(whatsapp_btn)
        contact_layout.addLayout(whatsapp_layout)
        
        # Email section removed as requested
        
        # Website
        website_layout = QHBoxLayout()
        website_icon = QLabel("🌐")
        website_icon.setFont(QFont("Arial", 14))
        website_icon.setStyleSheet("color: #D4AF37;")
        website_text = QLabel("الموقع الإلكتروني: www.albash-software.com")
        website_text.setFont(QFont("Arial", 12))
        website_text.setStyleSheet("color: white;")
        website_layout.addWidget(website_icon)
        website_layout.addWidget(website_text)
        website_layout.addStretch()
        contact_layout.addLayout(website_layout)
        
        company_layout.addLayout(contact_layout)
        layout.addWidget(company_frame)
        
        # Copyright
        copyright_frame = QFrame()
        copyright_frame.setStyleSheet("""
            QFrame {
                background-color: #f8f9fa;
                border-radius: 10px;
                padding: 15px;
                border: 1px solid #dee2e6;
            }
        """)
        copyright_layout = QVBoxLayout(copyright_frame)
        
        copyright_text = QLabel("© 2025 شركة الباش البرمجية. جميع الحقوق محفوظة.")
        copyright_text.setAlignment(Qt.AlignCenter)
        copyright_text.setFont(QFont("Arial", 10, QFont.Bold))
        copyright_text.setStyleSheet("color: #6c757d;")
        copyright_layout.addWidget(copyright_text)
        
        rights_text = QLabel("هذا البرنامج محمي بموجب قوانين حقوق الطبع والنشر الدولية")
        rights_text.setAlignment(Qt.AlignCenter)
        rights_text.setFont(QFont("Arial", 9))
        rights_text.setStyleSheet("color: #6c757d;")
        copyright_layout.addWidget(rights_text)
        
        layout.addWidget(copyright_frame)
        
        # Close button
        close_btn = QPushButton("إغلاق")
        close_btn.setFixedSize(100, 40)
        close_btn.setFont(QFont("Arial", 12, QFont.Bold))
        close_btn.setStyleSheet("""
            QPushButton {
                background-color: #1E2A38;
                color: white;
                border: none;
                border-radius: 8px;
            }
            QPushButton:hover {
                background-color: #D4AF37;
            }
        """)
        close_btn.clicked.connect(self.accept)
        
        button_layout = QHBoxLayout()
        button_layout.addStretch()
        button_layout.addWidget(close_btn)
        button_layout.addStretch()
        layout.addLayout(button_layout)
    
    def open_whatsapp(self):
        """Open WhatsApp with the company number"""
        try:
            phone_number = "9647715086335"  # Iraq country code + number
            message = "مرحباً، أريد الاستفسار عن نظام إدارة المطاعم"
            url = f"https://wa.me/{phone_number}?text={message}"
            webbrowser.open(url)
        except Exception as e:
            QMessageBox.information(self, "معلومات", "يرجى فتح واتساب والاتصال بالرقم: 07715086335")

class SplashScreen(QSplashScreen):
    def __init__(self):
        super().__init__()
        self.setFixedSize(600, 400)
        self.setup_ui()
        
        # Center the splash screen
        screen = QApplication.primaryScreen().geometry()
        x = (screen.width() - self.width()) // 2
        y = (screen.height() - self.height()) // 2
        self.move(x, y)
    
    def setup_ui(self):
        """Setup splash screen UI"""
        # Create pixmap for splash screen
        pixmap = QPixmap(600, 400)
        pixmap.fill(QColor("#1E2A38"))
        
        painter = QPainter(pixmap)
        painter.setRenderHint(QPainter.Antialiasing)
        
        # Background gradient
        gradient = QLinearGradient(0, 0, 600, 400)
        gradient.setColorAt(0, QColor("#1E2A38"))
        gradient.setColorAt(1, QColor("#D4AF37"))
        painter.fillRect(0, 0, 600, 400, gradient)
        
        # Logo
        painter.setPen(QColor("white"))
        painter.setFont(QFont("Arial", 72, QFont.Bold))
        painter.drawText(QRect(0, 100, 600, 100), Qt.AlignCenter, "🍽️")
        
        # App name
        painter.setFont(QFont("Arial", 24, QFont.Bold))
        painter.drawText(QRect(0, 200, 600, 40), Qt.AlignCenter, "نظام إدارة مطعم النوفوس")
        
        # Company name
        painter.setFont(QFont("Arial", 16))
        painter.drawText(QRect(0, 250, 600, 30), Qt.AlignCenter, "شركة الباش البرمجية")
        
        # Loading text
        painter.setFont(QFont("Arial", 12))
        painter.drawText(QRect(0, 320, 600, 20), Qt.AlignCenter, "جاري التحميل...")
        
        # Copyright
        painter.setFont(QFont("Arial", 10))
        painter.drawText(QRect(0, 360, 600, 20), Qt.AlignCenter, "© 2025 جميع الحقوق محفوظة")
        
        painter.end()
        
        self.setPixmap(pixmap)
    
    def showMessage(self, message, alignment=Qt.AlignBottom | Qt.AlignCenter, color=QColor("white")):
        """Show loading message"""
        super().showMessage(message, alignment, color)
