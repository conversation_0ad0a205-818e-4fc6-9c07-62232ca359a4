{"name": "@serialport/stream", "version": "12.0.0", "main": "./dist/index.js", "types": "./dist/index.d.ts", "scripts": {"build": "tsc --build tsconfig-build.json"}, "dependencies": {"@serialport/bindings-interface": "1.2.2", "debug": "4.3.4"}, "devDependencies": {"@serialport/binding-mock": "^10.2.2", "typescript": "5.2.2"}, "engines": {"node": ">=12.0.0"}, "publishConfig": {"access": "public"}, "license": "MIT", "repository": {"type": "git", "url": "git://github.com/serialport/node-serialport.git"}, "funding": "https://opencollective.com/serialport/donate", "gitHead": "f7e7bd53f9578a26c4f44cc1949fef396dc064c7"}