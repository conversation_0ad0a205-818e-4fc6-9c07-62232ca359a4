{"name": "@serialport/bindings-cpp", "description": "SerialPort Hardware bindings for node serialport written in c++", "version": "12.0.1", "main": "./dist/index.js", "types": "./dist/index.d.ts", "keywords": ["serialport-binding", "COM", "com port", "hardware", "iot", "modem", "serial port", "serial", "serialport", "tty", "UART"], "dependencies": {"@serialport/bindings-interface": "1.2.2", "@serialport/parser-readline": "11.0.0", "debug": "4.3.4", "node-addon-api": "7.0.0", "node-gyp-build": "4.6.0"}, "devDependencies": {"@semantic-release/exec": "6.0.3", "@serialport/binding-mock": "10.2.2", "@types/chai": "4.3.5", "@types/chai-subset": "1.3.3", "@types/debug": "4.1.8", "@types/mocha": "10.0.1", "@types/node": "18.16.20", "@typescript-eslint/eslint-plugin": "6.1.0", "@typescript-eslint/parser": "6.1.0", "cc": "3.0.1", "chai": "4.3.7", "chai-subset": "1.6.0", "esbuild": "0.18.15", "esbuild-register": "3.4.2", "eslint": "8.45.0", "mocha": "10.2.0", "node-abi": "3.45.0", "node-gyp": "9.4.0", "nyc": "15.1.0", "prebuildify": "5.0.1", "prebuildify-cross": "5.0.0", "semantic-release": "21.0.7", "shx": "0.3.4", "sinon": "15.2.0", "typescript": "5.1.6"}, "engines": {"node": ">=16.0.0"}, "scripts": {"build": "rm -rf dist && tsc -p tsconfig-build.json", "install": "node-gyp-build", "prebuildify": "prebuildify --napi --target 14.0.0 --force --strip --verbose", "prebuildify-cross": "prebuildify-cross --napi --target 14.0.0 --force --strip --verbose", "rebuild": "node-gyp rebuild", "format": "eslint lib test bin --fix", "lint": "eslint lib test bin && cc --verbose", "test": "nyc --reporter lcov --reporter text mocha", "test:arduino": "TEST_PORT=$(./bin/find-arduino.ts) npm test", "test:watch": "mocha -w", "semantic-release": "semantic-release", "typecheck": "tsc"}, "publishConfig": {"access": "public"}, "license": "MIT", "gypfile": true, "cc": {"filter": ["legal/copyright", "build/include"], "files": ["src/*.cpp", "src/*.h"], "linelength": "120"}, "binary": {"napi_versions": [6]}, "repository": {"type": "git", "url": "https://github.com/serialport/bindings-cpp.git"}, "funding": "https://opencollective.com/serialport/donate", "changelog": {"labels": {"breaking": ":boom: BREAKING CHANGES :boom:", "feature-request": "Features", "bug": "Bug Fixes", "docs": "Documentation", "internal": "Chores"}}, "mocha": {"bail": true, "require": ["esbuild-register"], "spec": "lib/**/*.test.*"}}