#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام إدارة مطعم النوفوس - شاشة تسجيل الدخول الجميلة
Restaurant Management System - Beautiful Login Screen

تطوير: شركة الباش البرمجية
Developer: Al-Bash Software Company
الهاتف/Phone: 07715086335
واتساب/WhatsApp: 07715086335
البريد الإلكتروني/Email: <EMAIL>
الموقع/Website: www.albash-software.com

© 2024 شركة الباش البرمجية. جميع الحقوق محفوظة.
© 2024 Al-Bash Software Company. All rights reserved.
"""

from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *

class LoginThread(QThread):
    """Thread for handling login API call"""
    login_result = pyqtSignal(bool, dict, str)
    
    def __init__(self, username, password, api_client):
        super().__init__()
        self.username = username
        self.password = password
        self.api_client = api_client
    
    def run(self):
        """Perform login API call"""
        try:
            result = self.api_client.login(self.username, self.password)
            if 'error' in result:
                self.login_result.emit(False, {}, result['error'])
            else:
                self.login_result.emit(True, result.get('user', {}), result.get('token', ''))
        except Exception as e:
            self.login_result.emit(False, {}, str(e))

class BeautifulLoginWindow(QWidget):
    def __init__(self, main_app):
        super().__init__()
        self.main_app = main_app
        self.setup_ui()
        self.setup_animations()
    
    def setup_ui(self):
        """Set up the beautiful login UI"""
        # Main layout
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # Background with gradient
        self.setStyleSheet("""
            QWidget {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #1E2A38, stop:0.3 #2C3E50, stop:0.7 #34495E, stop:1 #D4AF37);
            }
        """)
        
        # Central container
        central_widget = QWidget()
        central_layout = QVBoxLayout(central_widget)
        central_layout.setAlignment(Qt.AlignCenter)
        central_layout.setSpacing(40)
        
        # Logo and title section
        self.create_header_section(central_layout)
        
        # Login form
        self.create_login_form(central_layout)
        
        # Footer
        self.create_footer_section(central_layout)
        
        main_layout.addWidget(central_widget)
    
    def create_header_section(self, parent_layout):
        """Create beautiful header with logo and title"""
        header_widget = QWidget()
        header_layout = QVBoxLayout(header_widget)
        header_layout.setAlignment(Qt.AlignCenter)
        header_layout.setSpacing(20)
        
        # Logo
        logo_label = QLabel("🍽️")
        logo_label.setAlignment(Qt.AlignCenter)
        logo_label.setFont(QFont("Arial", 80, QFont.Bold))
        logo_label.setStyleSheet("""
            QLabel {
                color: white;
                text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
            }
        """)
        header_layout.addWidget(logo_label)
        
        # Main title
        title_label = QLabel("نظام إدارة مطعم النوفوس")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setFont(QFont("Arial", 28, QFont.Bold))
        title_label.setStyleSheet("""
            QLabel {
                color: white;
                text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
                margin: 10px;
            }
        """)
        header_layout.addWidget(title_label)
        
        # Subtitle
        subtitle_label = QLabel("Restaurant Management System")
        subtitle_label.setAlignment(Qt.AlignCenter)
        subtitle_label.setFont(QFont("Arial", 16))
        subtitle_label.setStyleSheet("""
            QLabel {
                color: #D4AF37;
                text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
                margin-bottom: 20px;
            }
        """)
        header_layout.addWidget(subtitle_label)
        
        parent_layout.addWidget(header_widget)
    
    def create_login_form(self, parent_layout):
        """Create beautiful login form"""
        # Form container with glass effect
        form_container = QFrame()
        form_container.setFixedSize(450, 350)
        form_container.setStyleSheet("""
            QFrame {
                background: rgba(255, 255, 255, 0.15);
                border-radius: 20px;
                border: 2px solid rgba(255, 255, 255, 0.3);
            }
        """)
        
        # Add shadow effect
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(30)
        shadow.setColor(QColor(0, 0, 0, 80))
        shadow.setOffset(0, 10)
        form_container.setGraphicsEffect(shadow)
        
        form_layout = QVBoxLayout(form_container)
        form_layout.setSpacing(25)
        form_layout.setContentsMargins(40, 40, 40, 40)
        
        # Form title
        form_title = QLabel("تسجيل الدخول")
        form_title.setAlignment(Qt.AlignCenter)
        form_title.setFont(QFont("Arial", 22, QFont.Bold))
        form_title.setStyleSheet("""
            QLabel {
                color: white;
                text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
                margin-bottom: 20px;
            }
        """)
        form_layout.addWidget(form_title)
        
        # Username field
        username_container = self.create_input_field("👤", "اسم المستخدم", "admin")
        self.username_input = username_container.findChild(QLineEdit)
        form_layout.addWidget(username_container)
        
        # Password field
        password_container = self.create_input_field("🔒", "كلمة المرور", "password", True)
        self.password_input = password_container.findChild(QLineEdit)
        form_layout.addWidget(password_container)
        
        # Login button
        self.login_btn = QPushButton("دخول")
        self.login_btn.setFixedHeight(50)
        self.login_btn.setFont(QFont("Arial", 16, QFont.Bold))
        self.login_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #D4AF37, stop:1 #F1C40F);
                color: #1E2A38;
                border: none;
                border-radius: 25px;
                font-weight: bold;
                text-shadow: 1px 1px 2px rgba(255,255,255,0.3);
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #F1C40F, stop:1 #D4AF37);
                transform: scale(1.02);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #B8941F, stop:1 #D4AF37);
            }
        """)
        self.login_btn.clicked.connect(self.handle_login)
        form_layout.addWidget(self.login_btn)
        
        parent_layout.addWidget(form_container, alignment=Qt.AlignCenter)
    
    def create_input_field(self, icon, placeholder, default_text="", is_password=False):
        """Create beautiful input field with icon"""
        container = QWidget()
        container.setFixedHeight(60)
        
        layout = QHBoxLayout(container)
        layout.setContentsMargins(15, 10, 15, 10)
        layout.setSpacing(15)
        
        # Icon
        icon_label = QLabel(icon)
        icon_label.setFont(QFont("Arial", 18))
        icon_label.setStyleSheet("color: #D4AF37;")
        icon_label.setFixedSize(30, 30)
        icon_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(icon_label)
        
        # Input field
        input_field = QLineEdit()
        input_field.setPlaceholderText(placeholder)
        input_field.setText(default_text)
        input_field.setFont(QFont("Arial", 14))
        
        if is_password:
            input_field.setEchoMode(QLineEdit.Password)
        
        input_field.setStyleSheet("""
            QLineEdit {
                background: rgba(255, 255, 255, 0.9);
                border: 2px solid rgba(255, 255, 255, 0.3);
                border-radius: 15px;
                padding: 10px 15px;
                font-size: 14px;
                color: #1E2A38;
            }
            QLineEdit:focus {
                border: 2px solid #D4AF37;
                background: rgba(255, 255, 255, 1.0);
            }
            QLineEdit::placeholder {
                color: #7f8c8d;
            }
        """)
        layout.addWidget(input_field)
        
        # Container styling
        container.setStyleSheet("""
            QWidget {
                background: rgba(255, 255, 255, 0.1);
                border-radius: 20px;
                border: 1px solid rgba(255, 255, 255, 0.2);
            }
        """)
        
        return container
    
    def create_footer_section(self, parent_layout):
        """Create beautiful footer"""
        footer_widget = QWidget()
        footer_layout = QVBoxLayout(footer_widget)
        footer_layout.setAlignment(Qt.AlignCenter)
        footer_layout.setSpacing(10)
        
        # Company name
        company_label = QLabel("شركة الباش البرمجية")
        company_label.setAlignment(Qt.AlignCenter)
        company_label.setFont(QFont("Arial", 14, QFont.Bold))
        company_label.setStyleSheet("""
            QLabel {
                color: #D4AF37;
                text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
            }
        """)
        footer_layout.addWidget(company_label)
        
        # Contact info
        contact_label = QLabel("للدعم: 07715086335")
        contact_label.setAlignment(Qt.AlignCenter)
        contact_label.setFont(QFont("Arial", 12))
        contact_label.setStyleSheet("""
            QLabel {
                color: white;
                text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
            }
        """)
        footer_layout.addWidget(contact_label)
        
        # Copyright
        copyright_label = QLabel("© 2024 جميع الحقوق محفوظة")
        copyright_label.setAlignment(Qt.AlignCenter)
        copyright_label.setFont(QFont("Arial", 10))
        copyright_label.setStyleSheet("""
            QLabel {
                color: rgba(255, 255, 255, 0.7);
                text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
            }
        """)
        footer_layout.addWidget(copyright_label)
        
        parent_layout.addWidget(footer_widget)
    
    def setup_animations(self):
        """Setup beautiful animations"""
        # Fade in animation for the whole window
        self.fade_animation = QPropertyAnimation(self, b"windowOpacity")
        self.fade_animation.setDuration(1000)
        self.fade_animation.setStartValue(0.0)
        self.fade_animation.setEndValue(1.0)
        self.fade_animation.setEasingCurve(QEasingCurve.OutCubic)
    
    def showEvent(self, event):
        """Override show event to start animations"""
        super().showEvent(event)
        self.fade_animation.start()
    
    def handle_login(self):
        """Handle login button click"""
        username = self.username_input.text().strip()
        password = self.password_input.text().strip()
        
        if not username or not password:
            QMessageBox.warning(self, "تحذير", "يرجى إدخال اسم المستخدم وكلمة المرور")
            return
        
        # Disable login button and show loading
        self.login_btn.setText("جاري تسجيل الدخول...")
        self.login_btn.setEnabled(False)
        
        # Start login thread
        self.login_thread = LoginThread(username, password, self.main_app.api_client)
        self.login_thread.login_result.connect(self.handle_login_result)
        self.login_thread.start()
    
    def handle_login_result(self, success, user_data, token_or_error):
        """Handle login result"""
        # Re-enable login button
        self.login_btn.setText("دخول")
        self.login_btn.setEnabled(True)
        
        if success:
            # Successful login
            self.main_app.login_success(user_data, token_or_error)
        else:
            # Login failed
            QMessageBox.critical(self, "خطأ في تسجيل الدخول", token_or_error)
