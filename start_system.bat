@echo off
setlocal enabledelayedexpansion
chcp 65001 >nul
title نظام إدارة مطعم النوفوس - تشغيل النظام
echo ========================================
echo    نظام إدارة مطعم النوفوس - تشغيل النظام
echo    Restaurant Management System - Start System
echo ========================================
echo.
echo    شركة الباش البرمجية - Al-Bash Software Company
echo    الهاتف/Phone: 07715086335
echo    واتساب/WhatsApp: 07715086335
echo    الموقع/Website: www.albash-software.com
echo.
echo ========================================
echo.

echo Checking server status...

:: Check if server is already running on port 3001
netstat -an | find ":3001" | find "LISTENING" >nul
if %errorlevel% equ 0 (
    echo ✅ Server already running on port 3001
    set "SERVER_RUNNING=1"
) else (
    echo ❌ Server not running, starting it...
    set "SERVER_RUNNING=0"
)
echo.

echo Checking waiter app status...
netstat -an | find ":3002" | find "LISTENING" >nul
if %errorlevel% equ 0 (
    echo ✅ Waiter app already running on port 3002
    set "WAITER_RUNNING=1"
) else (
    echo ❌ Waiter app not running, starting it...
    set "WAITER_RUNNING=0"
)
echo.

if "!SERVER_RUNNING!"=="0" (
    echo Starting server...

    if not exist "server\server.js" (
        echo ❌ Server file not found! Please run install_requirements.bat first
        pause
        exit /b 1
    )

    pushd server
    echo Starting server in background...
    start /min "Novos Server" cmd /c "node server.js"
    popd

    echo Waiting for server to start...
    timeout /t 3 /nobreak >nul

    netstat -an | find ":3001" | find "LISTENING" >nul
    if %errorlevel% equ 0 (
        echo ✅ Server started successfully
    ) else (
        echo ❌ Failed to start server
        pause
        exit /b 1
    )
) else (
    echo Server already running, skipping...
)
echo.

if "!WAITER_RUNNING!"=="0" (
    echo Starting waiter app...

    if not exist "waiter-app\index.html" (
        echo ❌ Waiter app files not found! Please run install_requirements.bat first
        pause
        exit /b 1
    )

    pushd waiter-app
    echo Starting waiter app in background...
    start /min "Novos Waiter App" cmd /c "npm start"
    popd

    echo Waiting for waiter app to start...
    timeout /t 5 /nobreak >nul

    netstat -an | find ":3002" | find "LISTENING" >nul
    if %errorlevel% equ 0 (
        echo ✅ Waiter app started successfully
    ) else (
        echo ❌ فشل في تشغيل موقع النادل
        echo ❌ Failed to start waiter app
        pause
        exit /b 1
    )
) else (
    echo Waiter app already running, skipping...
)
echo.

echo ========================================
echo ✅ All services started successfully!
echo ========================================
echo.
echo Available services:
echo.
echo 🖥️  Server: http://localhost:3001
echo 📱 Waiter App: http://localhost:3002
echo ========================================
echo.

echo Starting Python application...
echo.

if not exist "main.py" (
    echo ❌ ملف main.py غير موجود!
    echo ❌ main.py file not found!
    pause
    exit /b 1
)

echo 🐍 تشغيل برنامج إدارة المطعم...
echo 🐍 Starting restaurant management application...
echo.

python main.py

echo.
echo ========================================
echo تم إغلاق برنامج إدارة المطعم
echo Restaurant management application closed
echo ========================================
echo.
echo الخدمات الأخرى لا تزال تعمل في الخلفية:
echo Other services are still running in background:
echo - السيرفر: http://localhost:3001
echo - موقع النادل: http://localhost:3002
echo.
echo لإيقاف جميع الخدمات، استخدم stop_system.bat
echo To stop all services, use stop_system.bat
echo.
pause
