const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const cors = require('cors');
const path = require('path');
const fs = require('fs');
const moment = require('moment');
const { v4: uuidv4 } = require('uuid');
const axios = require('axios');

const app = express();
const server = http.createServer(app);
const io = socketIo(server, {
    cors: {
        origin: "*",
        methods: ["GET", "POST"]
    }
});

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.static('public'));

// Data file paths (same as main server)
const DATA_DIR = '../data';
const ORDERS_FILE = path.join(DATA_DIR, 'orders.json');
const MENU_FILE = path.join(DATA_DIR, 'menu.json');
const HALLS_FILE = path.join(DATA_DIR, 'halls.json');

// Ensure data directory exists
if (!fs.existsSync(DATA_DIR)) {
    fs.mkdirSync(DATA_DIR, { recursive: true });
}

// Helper functions to read/write data
const readDataFile = (filePath, defaultData = {}) => {
    try {
        if (fs.existsSync(filePath)) {
            const data = fs.readFileSync(filePath, 'utf8');
            return JSON.parse(data);
        }
        return defaultData;
    } catch (error) {
        console.error(`Error reading ${filePath}:`, error);
        return defaultData;
    }
};

const writeDataFile = (filePath, data) => {
    try {
        fs.writeFileSync(filePath, JSON.stringify(data, null, 2), 'utf8');
        return true;
    } catch (error) {
        console.error(`Error writing ${filePath}:`, error);
        return false;
    }
};

// Socket.io connection handling
io.on('connection', (socket) => {
    console.log('Waiter connected:', socket.id);
    
    // Join waiter room for notifications
    socket.join('waiters');
    
    socket.on('disconnect', () => {
        console.log('Waiter disconnected:', socket.id);
    });
});

// API Routes

// Get menu - proxy to main server
app.get('/api/menu', async (req, res) => {
    try {
        const axios = require('axios');
        const response = await axios.get('http://localhost:3002/api/menu');

        console.log('Menu data loaded from main server:', response.data);
        res.json(response.data);
    } catch (error) {
        console.error('Error loading menu from main server:', error);

        // Fallback to local file
        try {
            const menuData = readDataFile(MENU_FILE, { sections: [] });

            // Ensure each section has items array
            const transformedData = {
                sections: menuData.sections.map(section => ({
                    id: section.id,
                    name: section.name,
                    items: section.items || []
                }))
            };

            console.log('Menu data loaded from local file:', transformedData);
            res.json(transformedData);
        } catch (localError) {
            console.error('Error loading menu from local file:', localError);
            res.status(500).json({ error: 'Failed to load menu' });
        }
    }
});

// Get halls and tables
app.get('/api/halls', (req, res) => {
    try {
        const hallsData = readDataFile(HALLS_FILE, { halls: [] });

        // Transform data to match expected format
        const transformedData = {
            halls: hallsData.halls.map(hall => ({
                id: hall.id,
                name: hall.name,
                tableCount: hall.tables ? hall.tables.length : 10 // Default to 10 if no tables defined
            }))
        };

        res.json(transformedData);
    } catch (error) {
        console.error('Error loading halls:', error);
        res.status(500).json({ error: 'Failed to load halls' });
    }
});

// Create waiter order (pending approval)
app.post('/api/waiter-orders', (req, res) => {
    try {
        const { type, hallId, tableNumber, customerPhone, customerAddress, items, notes, waiterName } = req.body;
        
        // Validate required fields
        if (!items || items.length === 0) {
            return res.status(400).json({ error: 'Order must contain at least one item' });
        }
        
        // Calculate total amount
        const totalAmount = items.reduce((sum, item) => {
            return sum + (item.price * item.quantity);
        }, 0);
        
        // Read current orders
        const ordersData = readDataFile(ORDERS_FILE, { orders: [] });
        
        // Create new waiter order (pending approval)
        const newOrder = {
            id: uuidv4(),
            orderNumber: ordersData.orders.length + 1,
            type,
            hallId: type === 'dine-in' ? hallId : null,
            tableNumber: type === 'dine-in' ? tableNumber : null,
            customerPhone: type === 'delivery' ? customerPhone : null,
            customerAddress: type === 'delivery' ? customerAddress : null,
            items,
            totalAmount: parseFloat(totalAmount.toFixed(2)),
            notes: notes || '',
            status: 'pending_approval', // Special status for waiter orders
            createdAt: moment().toISOString(),
            waiterName: waiterName || 'نادل',
            isWaiterOrder: true
        };
        
        // Add to orders
        ordersData.orders.push(newOrder);
        
        // Save to file
        if (writeDataFile(ORDERS_FILE, ordersData)) {
            // Notify cashier via socket
            io.emit('new_waiter_order', {
                order: newOrder,
                message: `طلب جديد من النادل: ${waiterName || 'نادل'}`
            });
            
            res.status(201).json({
                success: true,
                order: newOrder,
                orderId: newOrder.id,
                message: 'تم إرسال الطلب للكاشير للموافقة'
            });
        } else {
            res.status(500).json({ error: 'Failed to save order' });
        }
        
    } catch (error) {
        console.error('Error creating waiter order:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});

// Get pending waiter orders
app.get('/api/waiter-orders/pending', (req, res) => {
    try {
        const ordersData = readDataFile(ORDERS_FILE, { orders: [] });
        const pendingOrders = ordersData.orders.filter(order => 
            order.isWaiterOrder && order.status === 'pending_approval'
        );
        
        res.json({ orders: pendingOrders });
    } catch (error) {
        res.status(500).json({ error: 'Failed to load pending orders' });
    }
});

// Update waiter order
app.put('/api/waiter-orders/:id', (req, res) => {
    try {
        const orderId = req.params.id;
        const { type, hallId, tableNumber, customerPhone, customerAddress, items, notes, waiterName } = req.body;

        const ordersData = readDataFile(ORDERS_FILE, { orders: [] });

        const orderIndex = ordersData.orders.findIndex(order => order.id === orderId);
        if (orderIndex === -1) {
            return res.status(404).json({ error: 'Order not found' });
        }

        // Check if order can be updated (only pending orders)
        if (ordersData.orders[orderIndex].status !== 'pending_approval') {
            return res.status(400).json({ error: 'Cannot update order that is not pending' });
        }

        // Calculate total amount
        let totalAmount = 0;
        items.forEach(item => {
            totalAmount += item.price * item.quantity;
        });

        // Update order
        ordersData.orders[orderIndex] = {
            ...ordersData.orders[orderIndex],
            type,
            hallId: type === 'dine-in' ? hallId : null,
            tableNumber: type === 'dine-in' ? tableNumber : null,
            customerPhone: type === 'delivery' ? customerPhone : null,
            customerAddress: type === 'delivery' ? customerAddress : null,
            items,
            totalAmount: parseFloat(totalAmount.toFixed(2)),
            notes: notes || '',
            waiterName: waiterName || 'نادل',
            updatedAt: moment().toISOString()
        };

        if (writeDataFile(ORDERS_FILE, ordersData)) {
            // Notify cashier about update
            io.emit('waiter_order_updated', {
                order: ordersData.orders[orderIndex],
                message: `تم تحديث الطلب #${ordersData.orders[orderIndex].orderNumber}`
            });

            res.json({
                success: true,
                order: ordersData.orders[orderIndex],
                message: 'تم تحديث الطلب بنجاح'
            });
        } else {
            res.status(500).json({ error: 'Failed to update order' });
        }

    } catch (error) {
        console.error('Error updating waiter order:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});

// Delete waiter order
app.delete('/api/waiter-orders/:id', (req, res) => {
    try {
        const orderId = req.params.id;
        const ordersData = readDataFile(ORDERS_FILE, { orders: [] });

        const orderIndex = ordersData.orders.findIndex(order => order.id === orderId);
        if (orderIndex === -1) {
            return res.status(404).json({ error: 'Order not found' });
        }

        // Check if order can be deleted (only pending orders)
        if (ordersData.orders[orderIndex].status !== 'pending_approval') {
            return res.status(400).json({ error: 'Cannot delete order that is not pending' });
        }

        const deletedOrder = ordersData.orders[orderIndex];

        // Remove order
        ordersData.orders.splice(orderIndex, 1);

        if (writeDataFile(ORDERS_FILE, ordersData)) {
            // Notify cashier about deletion
            io.emit('waiter_order_deleted', {
                orderNumber: deletedOrder.orderNumber,
                message: `تم إلغاء الطلب #${deletedOrder.orderNumber}`
            });

            res.json({
                success: true,
                message: 'تم إلغاء الطلب بنجاح'
            });
        } else {
            res.status(500).json({ error: 'Failed to delete order' });
        }

    } catch (error) {
        console.error('Error deleting waiter order:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});

// Approve waiter order (cashier endpoint)
app.post('/api/waiter-orders/:id/approve', (req, res) => {
    try {
        const orderId = req.params.id;
        const ordersData = readDataFile(ORDERS_FILE, { orders: [] });
        
        const orderIndex = ordersData.orders.findIndex(order => order.id === orderId);
        if (orderIndex === -1) {
            return res.status(404).json({ error: 'Order not found' });
        }
        
        // Update order status
        ordersData.orders[orderIndex].status = 'approved';
        ordersData.orders[orderIndex].approvedAt = moment().toISOString();
        
        if (writeDataFile(ORDERS_FILE, ordersData)) {
            // Notify waiter
            io.to('waiters').emit('order_approved', {
                orderId: orderId,
                orderNumber: ordersData.orders[orderIndex].orderNumber
            });
            
            res.json({ 
                success: true, 
                order: ordersData.orders[orderIndex],
                message: 'تم الموافقة على الطلب'
            });
        } else {
            res.status(500).json({ error: 'Failed to approve order' });
        }
        
    } catch (error) {
        res.status(500).json({ error: 'Failed to approve order' });
    }
});

// Reject waiter order (cashier endpoint)
app.post('/api/waiter-orders/:id/reject', (req, res) => {
    try {
        const orderId = req.params.id;
        const { reason } = req.body;
        const ordersData = readDataFile(ORDERS_FILE, { orders: [] });
        
        const orderIndex = ordersData.orders.findIndex(order => order.id === orderId);
        if (orderIndex === -1) {
            return res.status(404).json({ error: 'Order not found' });
        }
        
        // Update order status
        ordersData.orders[orderIndex].status = 'rejected';
        ordersData.orders[orderIndex].rejectedAt = moment().toISOString();
        ordersData.orders[orderIndex].rejectionReason = reason || 'لم يتم تحديد السبب';
        
        if (writeDataFile(ORDERS_FILE, ordersData)) {
            // Notify waiter
            io.to('waiters').emit('order_rejected', {
                orderId: orderId,
                orderNumber: ordersData.orders[orderIndex].orderNumber,
                reason: reason
            });
            
            res.json({ 
                success: true, 
                message: 'تم رفض الطلب'
            });
        } else {
            res.status(500).json({ error: 'Failed to reject order' });
        }
        
    } catch (error) {
        res.status(500).json({ error: 'Failed to reject order' });
    }
});

// Serve the waiter web app
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// Get order history
app.get('/api/waiter-orders/history', (req, res) => {
    try {
        const ordersData = readDataFile(ORDERS_FILE, { orders: [] });
        const historyOrders = ordersData.orders.filter(order =>
            order.isWaiterOrder && (order.status === 'approved' || order.status === 'rejected')
        ).sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));

        res.json({ orders: historyOrders });
    } catch (error) {
        res.status(500).json({ error: 'Failed to load order history' });
    }
});

// Resend order as modification
app.post('/api/waiter-orders/:id/resend', (req, res) => {
    try {
        const orderId = req.params.id;
        const ordersData = readDataFile(ORDERS_FILE, { orders: [] });

        const orderIndex = ordersData.orders.findIndex(order => order.id === orderId);
        if (orderIndex === -1) {
            return res.status(404).json({ error: 'Order not found' });
        }

        const originalOrder = ordersData.orders[orderIndex];

        // Check if order can be resent (only approved orders)
        if (originalOrder.status !== 'approved') {
            return res.status(400).json({ error: 'Only approved orders can be resent' });
        }

        // Create new order based on original
        const newOrder = {
            id: uuidv4(),
            orderNumber: ordersData.orders.length + 1,
            type: originalOrder.type,
            hallId: originalOrder.hallId,
            tableNumber: originalOrder.tableNumber,
            customerPhone: originalOrder.customerPhone,
            customerAddress: originalOrder.customerAddress,
            items: originalOrder.items,
            totalAmount: originalOrder.totalAmount,
            notes: originalOrder.notes || '',
            status: 'pending_approval',
            waiterName: originalOrder.waiterName,
            isWaiterOrder: true,
            isModification: true,
            originalOrderId: orderId,
            createdAt: moment().toISOString()
        };

        // Add to orders
        ordersData.orders.push(newOrder);

        // Save to file
        if (writeDataFile(ORDERS_FILE, ordersData)) {
            // Notify cashier via socket
            io.emit('new_waiter_order', {
                order: newOrder,
                message: `طلب معدل من النادل: ${newOrder.waiterName || 'نادل'}`
            });

            res.json({
                success: true,
                order: newOrder,
                message: 'تم إعادة إرسال الطلب كتعديل'
            });
        } else {
            res.status(500).json({ error: 'Failed to resend order' });
        }

    } catch (error) {
        console.error('Error resending order:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});

// Get single order by ID
app.get('/api/waiter-orders/:id', (req, res) => {
    try {
        const orderId = req.params.id;
        const ordersData = readDataFile(ORDERS_FILE, { orders: [] });

        const order = ordersData.orders.find(order => order.id === orderId);
        if (!order) {
            return res.status(404).json({ error: 'Order not found' });
        }

        res.json({
            success: true,
            order: order
        });

    } catch (error) {
        console.error('Error getting order:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});

// Edit invoice (update approved order)
app.put('/api/waiter-orders/:id/edit-invoice', (req, res) => {
    try {
        const orderId = req.params.id;
        const { type, hallId, tableNumber, customerPhone, customerAddress, items, notes, waiterName } = req.body;

        const ordersData = readDataFile(ORDERS_FILE, { orders: [] });

        const orderIndex = ordersData.orders.findIndex(order => order.id === orderId);
        if (orderIndex === -1) {
            return res.status(404).json({ error: 'Order not found' });
        }

        const originalOrder = ordersData.orders[orderIndex];

        // Calculate total amount
        let totalAmount = 0;
        items.forEach(item => {
            totalAmount += item.price * item.quantity;
        });

        // Create new order as modification of approved invoice
        const newOrder = {
            id: uuidv4(),
            orderNumber: ordersData.orders.length + 1,
            type,
            hallId: type === 'dine-in' ? hallId : null,
            tableNumber: type === 'dine-in' ? tableNumber : null,
            customerPhone: type === 'delivery' ? customerPhone : null,
            customerAddress: type === 'delivery' ? customerAddress : null,
            items,
            totalAmount: parseFloat(totalAmount.toFixed(2)),
            notes: notes || '',
            status: 'pending_approval',
            waiterName: waiterName || 'نادل',
            isWaiterOrder: true,
            isInvoiceEdit: true, // Flag to indicate this is an invoice edit
            originalInvoiceId: orderId,
            originalOrderNumber: originalOrder.orderNumber,
            createdAt: moment().toISOString()
        };

        // Add to orders
        ordersData.orders.push(newOrder);

        // Save to file
        if (writeDataFile(ORDERS_FILE, ordersData)) {
            // Notify cashier via socket
            io.emit('invoice_edit_request', {
                order: newOrder,
                originalOrder: originalOrder,
                message: `طلب تعديل فاتورة #${originalOrder.orderNumber} من النادل: ${waiterName || 'نادل'}`
            });

            res.json({
                success: true,
                order: newOrder,
                message: 'تم إرسال طلب تعديل الفاتورة للكاشير'
            });
        } else {
            res.status(500).json({ error: 'Failed to save invoice edit request' });
        }

    } catch (error) {
        console.error('Error editing invoice:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});

// Health check
app.get('/health', (req, res) => {
    res.json({ status: 'OK', service: 'Waiter App', timestamp: moment().toISOString() });
});

const PORT = process.env.PORT || 3001;
server.listen(PORT, () => {
    console.log(`🍽️  Waiter app server running on port ${PORT}`);
    console.log(`📱 Waiter interface: http://localhost:${PORT}`);
});
