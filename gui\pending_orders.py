from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *
import requests
import json
from datetime import datetime

class PendingOrdersWindow(QWidget):
    def __init__(self, main_app):
        super().__init__()
        self.main_app = main_app
        self.pending_orders = []
        self.init_ui()
        self.load_pending_orders()
        
        # Auto refresh every 30 seconds
        self.refresh_timer = QTimer()
        self.refresh_timer.timeout.connect(self.load_pending_orders)
        self.refresh_timer.start(30000)  # 30 seconds
    
    def init_ui(self):
        """Initialize the user interface"""
        self.setWindowTitle("طلبات النوادل المعلقة")
        self.setGeometry(100, 100, 1200, 800)
        
        # Main layout
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)
        
        # Header
        self.create_header(main_layout)
        
        # Orders table
        self.create_orders_table(main_layout)
        
        # Footer buttons
        self.create_footer(main_layout)
    
    def create_header(self, parent_layout):
        """Create header section"""
        header_frame = QFrame()
        header_frame.setStyleSheet("""
            QFrame {
                background-color: #1E2A38;
                border-radius: 10px;
                padding: 15px;
            }
        """)
        
        header_layout = QHBoxLayout(header_frame)
        
        # Title
        title_label = QLabel("طلبات النوادل المعلقة")
        title_label.setFont(QFont("Arial", 24, QFont.Bold))
        title_label.setStyleSheet("color: #D4AF37;")
        header_layout.addWidget(title_label)
        
        header_layout.addStretch()
        
        # Refresh button
        refresh_btn = QPushButton("🔄 تحديث")
        refresh_btn.setFont(QFont("Arial", 12, QFont.Bold))
        refresh_btn.setStyleSheet("""
            QPushButton {
                background-color: #D4AF37;
                color: #1E2A38;
                border: none;
                border-radius: 8px;
                padding: 10px 20px;
            }
            QPushButton:hover {
                background-color: #B8941F;
            }
        """)
        refresh_btn.clicked.connect(self.load_pending_orders)
        header_layout.addWidget(refresh_btn)
        
        # Count label
        self.count_label = QLabel("0 طلب معلق")
        self.count_label.setFont(QFont("Arial", 14, QFont.Bold))
        self.count_label.setStyleSheet("color: white; margin-left: 20px;")
        header_layout.addWidget(self.count_label)
        
        parent_layout.addWidget(header_frame)
    
    def create_orders_table(self, parent_layout):
        """Create orders table"""
        self.orders_table = QTableWidget()
        self.orders_table.setColumnCount(7)
        self.orders_table.setHorizontalHeaderLabels([
            "رقم الطلب", "النادل", "النوع", "التفاصيل", "المجموع", "الوقت", "الإجراءات"
        ])
        
        # Table styling
        self.orders_table.setStyleSheet("""
            QTableWidget {
                background-color: white;
                border: none;
                gridline-color: #f0f0f0;
                font-size: 12px;
            }
            QTableWidget::item {
                padding: 18px 12px;
                border-bottom: 1px solid #f0f0f0;
                font-size: 11px;
                min-height: 35px;
            }
            QTableWidget::item:selected {
                background-color: #e3f2fd;
            }
            QHeaderView::section {
                background-color: #1E2A38;
                color: white;
                padding: 12px;
                border: none;
                font-weight: bold;
                font-size: 12px;
            }
        """)
        
        # Set table properties
        header = self.orders_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.Stretch)
        
        # Set row height for better button visibility
        self.orders_table.verticalHeader().setDefaultSectionSize(80)
        self.orders_table.verticalHeader().setVisible(False)
        
        parent_layout.addWidget(self.orders_table)
    
    def create_footer(self, parent_layout):
        """Create footer buttons"""
        footer_layout = QHBoxLayout()
        
        # Back button
        back_btn = QPushButton("🔙 العودة")
        back_btn.setFont(QFont("Arial", 14, QFont.Bold))
        back_btn.setFixedSize(150, 50)
        back_btn.setStyleSheet("""
            QPushButton {
                background-color: #6c757d;
                color: white;
                border: none;
                border-radius: 8px;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #5a6268;
            }
        """)
        back_btn.clicked.connect(self.close)
        footer_layout.addWidget(back_btn)
        
        footer_layout.addStretch()
        
        # Auto refresh status
        self.auto_refresh_label = QLabel("التحديث التلقائي: كل 30 ثانية")
        self.auto_refresh_label.setFont(QFont("Arial", 10))
        self.auto_refresh_label.setStyleSheet("color: #6c757d;")
        footer_layout.addWidget(self.auto_refresh_label)
        
        parent_layout.addLayout(footer_layout)
    
    def load_pending_orders(self):
        """Load pending orders from waiter app"""
        try:
            response = requests.get('http://localhost:3001/api/waiter-orders/pending', timeout=5)
            if response.status_code == 200:
                data = response.json()
                self.pending_orders = data.get('orders', [])
                self.update_orders_table()
                self.count_label.setText(f"{len(self.pending_orders)} طلب معلق")
            else:
                self.show_message("خطأ في تحميل الطلبات", "error")
        except requests.exceptions.RequestException as e:
            print(f"Error loading pending orders: {e}")
            self.show_message("خطأ في الاتصال بخادم النوادل", "error")
    
    def update_orders_table(self):
        """Update the orders table with current data"""
        self.orders_table.setRowCount(len(self.pending_orders))
        
        for row, order in enumerate(self.pending_orders):
            # Order number
            self.orders_table.setItem(row, 0, QTableWidgetItem(f"#{order.get('orderNumber', 'N/A')}"))
            
            # Waiter name
            self.orders_table.setItem(row, 1, QTableWidgetItem(order.get('waiterName', 'غير محدد')))
            
            # Order type
            order_type = self.get_order_type_text(order.get('type', ''))
            self.orders_table.setItem(row, 2, QTableWidgetItem(order_type))
            
            # Details
            details = self.get_order_details(order)
            self.orders_table.setItem(row, 3, QTableWidgetItem(details))
            
            # Total amount
            total = order.get('totalAmount', 0)
            total_text = f"{int(total):,}".replace(',', '.') + " دينار"
            self.orders_table.setItem(row, 4, QTableWidgetItem(total_text))
            
            # Time
            created_at = order.get('createdAt', '')
            if created_at:
                try:
                    dt = datetime.fromisoformat(created_at.replace('Z', '+00:00'))
                    time_text = dt.strftime('%H:%M:%S')
                except:
                    time_text = created_at
            else:
                time_text = 'غير محدد'
            self.orders_table.setItem(row, 5, QTableWidgetItem(time_text))
            
            # Actions
            actions_widget = self.create_actions_widget(order)
            self.orders_table.setCellWidget(row, 6, actions_widget)
    
    def create_actions_widget(self, order):
        """Create actions widget for each order"""
        widget = QWidget()
        layout = QHBoxLayout(widget)
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(5)
        
        # View button
        view_btn = QPushButton("عرض")
        view_btn.setFont(QFont("Arial", 9))
        view_btn.setMinimumSize(55, 28)
        view_btn.setStyleSheet("""
            QPushButton {
                background-color: #17a2b8;
                color: white;
                border: none;
                border-radius: 4px;
                font-size: 9px;
                font-weight: bold;
                padding: 4px 8px;
                margin: 1px;
            }
            QPushButton:hover {
                background-color: #138496;
            }
        """)
        view_btn.clicked.connect(lambda: self.view_order(order))
        layout.addWidget(view_btn)
        
        # Approve button
        approve_btn = QPushButton("موافقة")
        approve_btn.setFont(QFont("Arial", 9))
        approve_btn.setMinimumSize(55, 28)
        approve_btn.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                border: none;
                border-radius: 4px;
                font-size: 9px;
                font-weight: bold;
                padding: 4px 8px;
                margin: 1px;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        approve_btn.clicked.connect(lambda: self.approve_order(order))
        layout.addWidget(approve_btn)
        
        # Reject button
        reject_btn = QPushButton("رفض")
        reject_btn.setFont(QFont("Arial", 9))
        reject_btn.setMinimumSize(55, 28)
        reject_btn.setStyleSheet("""
            QPushButton {
                background-color: #dc3545;
                color: white;
                border: none;
                border-radius: 4px;
                font-size: 9px;
                font-weight: bold;
                padding: 4px 8px;
                margin: 1px;
            }
            QPushButton:hover {
                background-color: #c82333;
            }
        """)
        reject_btn.clicked.connect(lambda: self.reject_order(order))
        layout.addWidget(reject_btn)
        
        return widget
    
    def get_order_type_text(self, order_type):
        """Get Arabic text for order type"""
        types = {
            'dine-in': 'طاولات',
            'takeaway': 'سفري',
            'delivery': 'دلفري'
        }
        return types.get(order_type, order_type)
    
    def get_order_details(self, order):
        """Get order details text"""
        details = []
        
        if order.get('type') == 'dine-in':
            details.append(f"طاولة {order.get('tableNumber', 'غير محدد')}")
        elif order.get('type') == 'delivery':
            phone = order.get('customerPhone', '')
            if phone:
                details.append(f"هاتف: {phone}")
        
        items_count = len(order.get('items', []))
        details.append(f"{items_count} صنف")
        
        return " - ".join(details)
    
    def view_order(self, order):
        """View order details"""
        # Create a simple message box with order details
        details = []
        details.append(f"رقم الطلب: #{order.get('orderNumber', 'N/A')}")
        details.append(f"النادل: {order.get('waiterName', 'غير محدد')}")
        details.append(f"النوع: {self.get_order_type_text(order.get('type', ''))}")

        if order.get('type') == 'dine-in':
            details.append(f"طاولة: {order.get('tableNumber', 'غير محدد')}")
        elif order.get('type') == 'delivery':
            details.append(f"هاتف: {order.get('customerPhone', '')}")
            details.append(f"العنوان: {order.get('customerAddress', '')}")

        details.append("\nالأصناف:")
        total = 0
        for item in order.get('items', []):
            name = item.get('name', '')
            quantity = item.get('quantity', 0)
            price = item.get('price', 0)
            item_total = quantity * price
            total += item_total
            details.append(f"• {name} - {quantity} × {int(price):,}".replace(',', '.') + f" = {int(item_total):,}".replace(',', '.'))

        details.append(f"\nالمجموع: {int(total):,}".replace(',', '.') + " دينار")

        if order.get('notes'):
            details.append(f"\nملاحظات: {order.get('notes')}")

        QMessageBox.information(self, "تفاصيل الطلب", "\n".join(details))
    
    def approve_order(self, order):
        """Approve the order"""
        reply = QMessageBox.question(
            self, 
            "تأكيد الموافقة",
            f"هل أنت متأكد من الموافقة على الطلب #{order.get('orderNumber')}؟",
            QMessageBox.Yes | QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            try:
                response = requests.post(
                    f"http://localhost:3001/api/waiter-orders/{order['id']}/approve",
                    timeout=5
                )
                if response.status_code == 200:
                    self.show_message("تم الموافقة على الطلب بنجاح", "success")
                    self.load_pending_orders()

                    # Add to main orders system (for editing and printing)
                    self.add_to_main_orders(order)
                else:
                    self.show_message("فشل في الموافقة على الطلب", "error")
            except requests.exceptions.RequestException as e:
                self.show_message("خطأ في الاتصال", "error")
    
    def reject_order(self, order):
        """Reject the order"""
        reason, ok = QInputDialog.getText(
            self, 
            "سبب الرفض", 
            f"أدخل سبب رفض الطلب #{order.get('orderNumber')}:"
        )
        
        if ok and reason.strip():
            try:
                response = requests.post(
                    f"http://localhost:3001/api/waiter-orders/{order['id']}/reject",
                    json={'reason': reason.strip()},
                    timeout=5
                )
                if response.status_code == 200:
                    self.show_message("تم رفض الطلب", "success")
                    self.load_pending_orders()
                else:
                    self.show_message("فشل في رفض الطلب", "error")
            except requests.exceptions.RequestException as e:
                self.show_message("خطأ في الاتصال", "error")
    
    def add_to_main_orders(self, order):
        """Add approved order to main orders system"""
        try:
            # Show the appropriate order window based on type
            order_type = order.get('type', 'dine-in')

            if order_type == 'dine-in':
                # Show tables window and create order
                self.main_app.show_tables()
                # Set the order data for editing
                if hasattr(self.main_app, 'tables_window'):
                    self.main_app.tables_window.load_waiter_order(order)
            elif order_type == 'takeaway':
                # Show takeaway window and create order
                self.main_app.show_takeaway()
                if hasattr(self.main_app, 'takeaway_window'):
                    self.main_app.takeaway_window.load_waiter_order(order)
            elif order_type == 'delivery':
                # Show delivery window and create order
                self.main_app.show_delivery()
                if hasattr(self.main_app, 'delivery_window'):
                    self.main_app.delivery_window.load_waiter_order(order)

            # Close pending orders window
            self.close()

        except Exception as e:
            print(f"Error adding to main orders: {e}")
            self.show_message(f"خطأ في إضافة الطلب: {str(e)}", "error")
    
    def show_message(self, message, msg_type="info"):
        """Show message to user"""
        if msg_type == "success":
            QMessageBox.information(self, "نجح", message)
        elif msg_type == "error":
            QMessageBox.critical(self, "خطأ", message)
        else:
            QMessageBox.information(self, "معلومات", message)
    
    def closeEvent(self, event):
        """Handle window close event"""
        self.refresh_timer.stop()
        event.accept()
