from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
                             QPushButton, QFrame, QLineEdit, QTabWidget,
                             QWidget, QMessageBox, QComboBox, QGroupBox,
                             QFormLayout, QTextEdit)
from PyQt5.QtCore import Qt, QThread, pyqtSignal
from PyQt5.QtGui import QFont

class SettingsLoadThread(QThread):
    """Thread for loading settings"""
    data_loaded = pyqtSignal(dict)
    
    def __init__(self, api_client):
        super().__init__()
        self.api_client = api_client
    
    def run(self):
        # Load current settings and printers
        settings_result = {'restaurant': {'name': 'مطعم النوفوس', 'phone': '+964 123 456 789', 'address': 'بغداد، العراق'}}
        printers_result = self.api_client.get_printers()
        
        result = {
            'settings': settings_result,
            'printers': printers_result
        }
        self.data_loaded.emit(result)

class SettingsDialog(QDialog):
    def __init__(self, main_app):
        super().__init__()
        self.main_app = main_app
        self.setWindowTitle("إعدادات النظام")
        self.setFixedSize(600, 500)
        self.setModal(True)
        self.setup_ui()
        self.load_settings()
    
    def setup_ui(self):
        """Set up the settings UI"""
        layout = QVBoxLayout()
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)
        
        # Title
        title = QLabel("إعدادات النظام")
        title.setAlignment(Qt.AlignCenter)
        title.setFont(QFont("Arial", 18, QFont.Bold))
        title.setStyleSheet("color: #1E2A38; margin-bottom: 20px;")
        layout.addWidget(title)
        
        # Tab widget
        self.tab_widget = QTabWidget()
        self.tab_widget.setStyleSheet("""
            QTabWidget::pane {
                border: 2px solid #ddd;
                border-radius: 8px;
                background-color: white;
            }
            QTabBar::tab {
                background-color: #f8f9fa;
                padding: 10px 20px;
                margin: 2px;
                border-radius: 5px;
                font-weight: bold;
            }
            QTabBar::tab:selected {
                background-color: #1E2A38;
                color: white;
            }
        """)
        
        # Restaurant settings tab
        self.create_restaurant_tab()
        
        # Printer settings tab
        self.create_printer_tab()
        
        layout.addWidget(self.tab_widget)
        
        # Buttons
        buttons_layout = QHBoxLayout()
        
        save_btn = QPushButton("حفظ الإعدادات")
        save_btn.setFont(QFont("Arial", 12, QFont.Bold))
        save_btn.setFixedHeight(40)
        save_btn.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                border: none;
                border-radius: 8px;
                padding: 10px 20px;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        save_btn.clicked.connect(self.save_settings)
        buttons_layout.addWidget(save_btn)
        
        cancel_btn = QPushButton("إلغاء")
        cancel_btn.setFont(QFont("Arial", 12, QFont.Bold))
        cancel_btn.setFixedHeight(40)
        cancel_btn.setStyleSheet("""
            QPushButton {
                background-color: #6c757d;
                color: white;
                border: none;
                border-radius: 8px;
                padding: 10px 20px;
            }
            QPushButton:hover {
                background-color: #545b62;
            }
        """)
        cancel_btn.clicked.connect(self.reject)
        buttons_layout.addWidget(cancel_btn)
        
        layout.addLayout(buttons_layout)
        self.setLayout(layout)
    
    def create_restaurant_tab(self):
        """Create restaurant settings tab"""
        restaurant_widget = QWidget()
        layout = QVBoxLayout(restaurant_widget)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)
        
        # Restaurant info group
        restaurant_group = QGroupBox("معلومات المطعم")
        restaurant_group.setFont(QFont("Arial", 12, QFont.Bold))
        restaurant_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #ddd;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)
        
        restaurant_form = QFormLayout(restaurant_group)
        restaurant_form.setSpacing(15)
        
        # Restaurant name
        self.restaurant_name_input = QLineEdit()
        self.restaurant_name_input.setFont(QFont("Arial", 12))
        self.restaurant_name_input.setStyleSheet(self.get_input_style())
        restaurant_form.addRow("اسم المطعم:", self.restaurant_name_input)
        
        # Restaurant phone
        self.restaurant_phone_input = QLineEdit()
        self.restaurant_phone_input.setFont(QFont("Arial", 12))
        self.restaurant_phone_input.setLayoutDirection(Qt.LeftToRight)
        self.restaurant_phone_input.setStyleSheet(self.get_input_style())
        restaurant_form.addRow("رقم الهاتف:", self.restaurant_phone_input)
        
        # Restaurant address
        self.restaurant_address_input = QTextEdit()
        self.restaurant_address_input.setFont(QFont("Arial", 12))
        self.restaurant_address_input.setFixedHeight(80)
        self.restaurant_address_input.setStyleSheet(self.get_input_style())
        restaurant_form.addRow("العنوان:", self.restaurant_address_input)
        
        layout.addWidget(restaurant_group)
        layout.addStretch()
        
        self.tab_widget.addTab(restaurant_widget, "معلومات المطعم")
    
    def create_printer_tab(self):
        """Create printer settings tab"""
        printer_widget = QWidget()
        layout = QVBoxLayout(printer_widget)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)
        
        # Printer assignment group
        printer_group = QGroupBox("تعيين الطابعات")
        printer_group.setFont(QFont("Arial", 12, QFont.Bold))
        printer_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #ddd;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)
        
        printer_form = QFormLayout(printer_group)
        printer_form.setSpacing(15)
        
        # Cashier printer
        self.cashier_printer_combo = QComboBox()
        self.cashier_printer_combo.setFont(QFont("Arial", 12))
        self.cashier_printer_combo.setStyleSheet(self.get_combo_style())
        printer_form.addRow("طابعة الكاشير:", self.cashier_printer_combo)
        
        # Kitchen printer
        self.kitchen_printer_combo = QComboBox()
        self.kitchen_printer_combo.setFont(QFont("Arial", 12))
        self.kitchen_printer_combo.setStyleSheet(self.get_combo_style())
        printer_form.addRow("طابعة المطبخ:", self.kitchen_printer_combo)
        
        # Grill printer
        self.grill_printer_combo = QComboBox()
        self.grill_printer_combo.setFont(QFont("Arial", 12))
        self.grill_printer_combo.setStyleSheet(self.get_combo_style())
        printer_form.addRow("طابعة المشاوي:", self.grill_printer_combo)
        
        layout.addWidget(printer_group)
        
        # Test buttons
        test_group = QGroupBox("اختبار الطابعات")
        test_group.setFont(QFont("Arial", 12, QFont.Bold))
        test_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #ddd;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)
        
        test_layout = QHBoxLayout(test_group)
        test_layout.setSpacing(10)
        
        test_cashier_btn = QPushButton("اختبار الكاشير")
        test_cashier_btn.setFont(QFont("Arial", 10))
        test_cashier_btn.setStyleSheet("""
            QPushButton {
                background-color: #007bff;
                color: white;
                border: none;
                border-radius: 5px;
                padding: 8px 15px;
            }
            QPushButton:hover {
                background-color: #0056b3;
            }
        """)
        test_cashier_btn.clicked.connect(lambda: self.test_printer('cashier'))
        test_layout.addWidget(test_cashier_btn)
        
        test_kitchen_btn = QPushButton("اختبار المطبخ")
        test_kitchen_btn.setFont(QFont("Arial", 10))
        test_kitchen_btn.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                border: none;
                border-radius: 5px;
                padding: 8px 15px;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        test_kitchen_btn.clicked.connect(lambda: self.test_printer('kitchen'))
        test_layout.addWidget(test_kitchen_btn)
        
        test_grill_btn = QPushButton("اختبار المشاوي")
        test_grill_btn.setFont(QFont("Arial", 10))
        test_grill_btn.setStyleSheet("""
            QPushButton {
                background-color: #fd7e14;
                color: white;
                border: none;
                border-radius: 5px;
                padding: 8px 15px;
            }
            QPushButton:hover {
                background-color: #e55a00;
            }
        """)
        test_grill_btn.clicked.connect(lambda: self.test_printer('grill'))
        test_layout.addWidget(test_grill_btn)
        
        layout.addWidget(test_group)
        layout.addStretch()
        
        self.tab_widget.addTab(printer_widget, "إعدادات الطابعات")
    
    def get_input_style(self):
        """Get input field style"""
        return """
            QLineEdit, QTextEdit {
                padding: 8px;
                border: 2px solid #ddd;
                border-radius: 5px;
                background-color: white;
                font-size: 12px;
            }
            QLineEdit:focus, QTextEdit:focus {
                border-color: #1E2A38;
            }
        """
    
    def get_combo_style(self):
        """Get combobox style"""
        return """
            QComboBox {
                padding: 8px;
                border: 2px solid #ddd;
                border-radius: 5px;
                background-color: white;
                font-size: 12px;
                min-width: 200px;
            }
            QComboBox:focus {
                border-color: #1E2A38;
            }
            QComboBox::drop-down {
                border: none;
            }
            QComboBox::down-arrow {
                image: none;
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-top: 5px solid #666;
            }
        """
    
    def load_settings(self):
        """Load current settings"""
        self.load_thread = SettingsLoadThread(self.main_app.api_client)
        self.load_thread.data_loaded.connect(self.handle_settings_loaded)
        self.load_thread.start()
    
    def handle_settings_loaded(self, result):
        """Handle loaded settings"""
        # Load restaurant settings
        if 'settings' in result:
            restaurant = result['settings'].get('restaurant', {})
            self.restaurant_name_input.setText(restaurant.get('name', ''))
            self.restaurant_phone_input.setText(restaurant.get('phone', ''))
            self.restaurant_address_input.setPlainText(restaurant.get('address', ''))
        
        # Load printer settings
        if 'printers' in result and 'availablePrinters' in result['printers']:
            printers = result['printers']['availablePrinters']
            assigned = result['printers'].get('assignedPrinters', {})
            
            # Populate printer combos
            printer_options = ["لا يوجد"] + [p['name'] for p in printers]
            
            self.cashier_printer_combo.clear()
            self.cashier_printer_combo.addItems(printer_options)
            
            self.kitchen_printer_combo.clear()
            self.kitchen_printer_combo.addItems(printer_options)
            
            self.grill_printer_combo.clear()
            self.grill_printer_combo.addItems(printer_options)
            
            # Set current assignments
            if assigned.get('cashier'):
                self.cashier_printer_combo.setCurrentText(assigned['cashier'])
            if assigned.get('kitchen'):
                self.kitchen_printer_combo.setCurrentText(assigned['kitchen'])
            if assigned.get('grill'):
                self.grill_printer_combo.setCurrentText(assigned['grill'])
    
    def save_settings(self):
        """Save settings"""
        try:
            # Save restaurant settings (would need API endpoint)
            restaurant_data = {
                'name': self.restaurant_name_input.text().strip(),
                'phone': self.restaurant_phone_input.text().strip(),
                'address': self.restaurant_address_input.toPlainText().strip()
            }
            
            # Save printer assignments
            if self.cashier_printer_combo.currentText() != "لا يوجد":
                self.main_app.api_client.assign_printer('cashier', self.cashier_printer_combo.currentText())
            
            if self.kitchen_printer_combo.currentText() != "لا يوجد":
                self.main_app.api_client.assign_printer('kitchen', self.kitchen_printer_combo.currentText())
            
            if self.grill_printer_combo.currentText() != "لا يوجد":
                self.main_app.api_client.assign_printer('grill', self.grill_printer_combo.currentText())
            
            QMessageBox.information(self, "نجح", "تم حفظ الإعدادات بنجاح")
            self.accept()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في حفظ الإعدادات: {str(e)}")
    
    def test_printer(self, printer_type):
        """Test printer"""
        try:
            result = self.main_app.api_client.test_printer(printer_type)
            if 'error' in result:
                QMessageBox.critical(self, "خطأ", f"فشل اختبار الطابعة: {result['error']}")
            else:
                QMessageBox.information(self, "نجح", f"تم اختبار طابعة {printer_type} بنجاح")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في اختبار الطابعة: {str(e)}")
