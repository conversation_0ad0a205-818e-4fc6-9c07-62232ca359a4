{"name": "restaurant-billing-backend", "version": "1.0.0", "description": "Restaurant billing system backend with Node.js and JSON database", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["restaurant", "billing", "pos", "thermal-printer"], "author": "", "license": "MIT", "dependencies": {"bcryptjs": "^2.4.3", "cors": "^2.8.5", "express": "^4.18.2", "jsonwebtoken": "^9.0.2", "moment": "^2.29.4", "node-thermal-printer": "^4.4.4", "serialport": "^12.0.0", "socket.io": "^4.8.1", "uuid": "^9.0.1"}, "devDependencies": {"nodemon": "^3.0.1"}}