from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                             QPushButton, QFrame)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

class TakeawayWindow(QWidget):
    def __init__(self, main_app):
        super().__init__()
        self.main_app = main_app
        self.setup_ui()
    
    def setup_ui(self):
        """Set up the takeaway UI"""
        layout = QVBoxLayout()
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)
        
        # Top bar
        self.create_top_bar(layout)
        
        # Main content
        main_content = QWidget()
        main_layout = QVBoxLayout(main_content)
        main_layout.setAlignment(Qt.AlignCenter)
        main_layout.setSpacing(40)
        
        # Title
        title = QLabel("طلبات السفري")
        title.setAlignment(Qt.AlignCenter)
        title.setFont(QFont("Arial", 24, QFont.Bold))
        title.setStyleSheet("color: #1E2A38;")
        main_layout.addWidget(title)
        
        # Start order button
        start_order_btn = QPushButton("بدء طلب جديد")
        start_order_btn.setFixedSize(300, 100)
        start_order_btn.setFont(QFont("Arial", 18, QFont.Bold))
        start_order_btn.setStyleSheet("""
            QPushButton {
                background-color: #1E2A38;
                color: white;
                border: none;
                border-radius: 15px;
            }
            QPushButton:hover {
                background-color: #D4AF37;
            }
        """)
        start_order_btn.clicked.connect(self.start_takeaway_order)
        main_layout.addWidget(start_order_btn)
        
        layout.addWidget(main_content)
        
        # Footer navigation
        self.create_footer_navigation(layout)
        
        self.setLayout(layout)
    
    def create_top_bar(self, parent_layout):
        """Create top navigation bar"""
        top_bar = QFrame()
        top_bar.setFixedHeight(80)
        top_bar.setStyleSheet("""
            QFrame {
                background-color: #1E2A38;
                border-bottom: 3px solid #D4AF37;
            }
        """)
        
        top_layout = QHBoxLayout(top_bar)
        top_layout.setContentsMargins(20, 10, 20, 10)
        
        # Notifications icon (left)
        notifications_btn = QPushButton("🔔")
        notifications_btn.setFixedSize(50, 50)
        notifications_btn.setStyleSheet("""
            QPushButton {
                background-color: transparent;
                color: #D4AF37;
                border: 2px solid #D4AF37;
                border-radius: 25px;
                font-size: 20px;
            }
            QPushButton:hover {
                background-color: #D4AF37;
                color: #1E2A38;
            }
        """)
        top_layout.addWidget(notifications_btn)
        
        # Center area
        center_widget = QWidget()
        center_layout = QVBoxLayout(center_widget)
        center_layout.setAlignment(Qt.AlignCenter)
        
        page_title = QLabel("السفري")
        page_title.setAlignment(Qt.AlignCenter)
        page_title.setFont(QFont("Arial", 18, QFont.Bold))
        page_title.setStyleSheet("color: white;")
        center_layout.addWidget(page_title)
        
        top_layout.addWidget(center_widget)
        
        # Settings icon (right)
        settings_btn = QPushButton("⚙️")
        settings_btn.setFixedSize(50, 50)
        settings_btn.setStyleSheet("""
            QPushButton {
                background-color: transparent;
                color: #D4AF37;
                border: 2px solid #D4AF37;
                border-radius: 25px;
                font-size: 20px;
            }
            QPushButton:hover {
                background-color: #D4AF37;
                color: #1E2A38;
            }
        """)
        top_layout.addWidget(settings_btn)
        
        parent_layout.addWidget(top_bar)
    
    def create_footer_navigation(self, parent_layout):
        """Create footer navigation bar"""
        footer = QFrame()
        footer.setFixedHeight(70)
        footer.setStyleSheet("""
            QFrame {
                background-color: #1E2A38;
                border-top: 2px solid #D4AF37;
            }
        """)
        
        footer_layout = QHBoxLayout(footer)
        footer_layout.setContentsMargins(20, 10, 20, 10)
        footer_layout.setSpacing(20)
        
        # Back button
        back_btn = QPushButton("🔙 العودة")
        back_btn.setFont(QFont("Arial", 12, QFont.Bold))
        back_btn.setStyleSheet("""
            QPushButton {
                background-color: transparent;
                color: #D4AF37;
                border: 2px solid #D4AF37;
                border-radius: 8px;
                padding: 8px 16px;
            }
            QPushButton:hover {
                background-color: #D4AF37;
                color: #1E2A38;
            }
        """)
        back_btn.clicked.connect(self.main_app.show_dashboard)
        footer_layout.addWidget(back_btn)
        
        footer_layout.addStretch()
        
        # Old invoices button
        invoices_btn = QPushButton("📜 عرض الفواتير القديمة")
        invoices_btn.setFont(QFont("Arial", 12, QFont.Bold))
        invoices_btn.setStyleSheet("""
            QPushButton {
                background-color: transparent;
                color: #D4AF37;
                border: 2px solid #D4AF37;
                border-radius: 8px;
                padding: 8px 16px;
            }
            QPushButton:hover {
                background-color: #D4AF37;
                color: #1E2A38;
            }
        """)
        invoices_btn.clicked.connect(self.main_app.show_invoices)
        footer_layout.addWidget(invoices_btn)
        
        # Sales summary button
        sales_btn = QPushButton("📊 مبيعات اليوم")
        sales_btn.setFont(QFont("Arial", 12, QFont.Bold))
        sales_btn.setStyleSheet("""
            QPushButton {
                background-color: transparent;
                color: #D4AF37;
                border: 2px solid #D4AF37;
                border-radius: 8px;
                padding: 8px 16px;
            }
            QPushButton:hover {
                background-color: #D4AF37;
                color: #1E2A38;
            }
        """)
        sales_btn.clicked.connect(self.main_app.show_sales)
        footer_layout.addWidget(sales_btn)
        
        parent_layout.addWidget(footer)
    
    def start_takeaway_order(self):
        """Start new takeaway order"""
        self.main_app.show_menu('takeaway')

    def load_waiter_order(self, order):
        """Load waiter order for editing"""
        try:
            from PyQt5.QtWidgets import QMessageBox
            from gui.order import OrderWindow

            # Create order window for takeaway
            order_window = OrderWindow(self.main_app, 'takeaway')

            # Load waiter order data
            order_window.load_waiter_order_data(order)

            order_window.show()

        except Exception as e:
            print(f"Error loading waiter order: {e}")
            from PyQt5.QtWidgets import QMessageBox
            QMessageBox.critical(self, "خطأ", f"خطأ في تحميل طلب النادل: {str(e)}")
