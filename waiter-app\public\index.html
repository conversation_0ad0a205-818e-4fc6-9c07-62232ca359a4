<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تطبيق النادل - مطعم النوفوس</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <h1>🍽️ تطبيق النادل</h1>
            <div class="waiter-info">
                <span id="waiterName">النادل</span>
                <div class="notifications" id="notifications">
                    <span class="notification-icon">🔔</span>
                    <span class="notification-count" id="notificationCount">0</span>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main-content">
        <div class="container">
            
            <!-- Order Type Selection -->
            <section class="order-type-section" id="orderTypeSection">
                <h2>اختر نوع الطلب</h2>
                <div class="order-type-buttons">
                    <button class="order-type-btn" onclick="selectOrderType('dine-in')">
                        <span class="icon">🍽️</span>
                        <span>طاولات</span>
                    </button>
                    <button class="order-type-btn" onclick="selectOrderType('takeaway')">
                        <span class="icon">🥡</span>
                        <span>سفري</span>
                    </button>
                    <button class="order-type-btn" onclick="selectOrderType('delivery')">
                        <span class="icon">🚗</span>
                        <span>دلفري</span>
                    </button>
                </div>
            </section>

            <!-- Table Selection (for dine-in) -->
            <section class="table-selection" id="tableSelection" style="display: none;">
                <h2>اختر الطاولة</h2>
                <div class="halls-container" id="hallsContainer">
                    <!-- Halls and tables will be loaded here -->
                </div>
                <button class="back-btn" onclick="goBack()">العودة</button>
            </section>

            <!-- Delivery Info (for delivery) -->
            <section class="delivery-info" id="deliveryInfo" style="display: none;">
                <h2>معلومات التوصيل</h2>
                <div class="form-group">
                    <label for="customerPhone">رقم الهاتف:</label>
                    <input type="tel" id="customerPhone" placeholder="أدخل رقم هاتف الزبون">
                </div>
                <div class="form-group">
                    <label for="customerAddress">العنوان:</label>
                    <textarea id="customerAddress" placeholder="أدخل عنوان التوصيل"></textarea>
                </div>
                <div class="form-actions">
                    <button class="continue-btn" onclick="continueToMenu()">متابعة</button>
                    <button class="back-btn" onclick="goBack()">العودة</button>
                </div>
            </section>

            <!-- Menu Section -->
            <section class="menu-section" id="menuSection" style="display: none;">
                <div class="menu-header">
                    <h2>القائمة</h2>
                    <div class="order-info" id="orderInfo"></div>
                </div>
                
                <div class="menu-content">
                    <!-- Menu Categories -->
                    <div class="menu-categories">
                        <h3>الأقسام</h3>
                        <div class="categories-list" id="categoriesList">
                            <!-- Categories will be loaded here -->
                        </div>
                    </div>

                    <!-- Menu Items -->
                    <div class="menu-items">
                        <h3>الأصناف</h3>
                        <div class="items-list" id="itemsList">
                            <!-- Items will be loaded here -->
                        </div>
                    </div>

                    <!-- Order Summary -->
                    <div class="order-summary">
                        <h3>ملخص الطلب</h3>
                        <div class="order-items" id="orderItems">
                            <!-- Order items will appear here -->
                        </div>
                        <div class="order-total">
                            <strong>المجموع: <span id="orderTotal">0</span> دينار</strong>
                        </div>
                        <div class="order-notes">
                            <label for="orderNotes">ملاحظات:</label>
                            <textarea id="orderNotes" placeholder="أضف ملاحظات للطلب..."></textarea>
                        </div>
                        <div class="order-actions">
                            <button class="send-order-btn" onclick="sendOrder()">إرسال للكاشير</button>
                            <button class="clear-order-btn" onclick="clearOrder()">مسح الطلب</button>
                            <button class="back-btn" onclick="goBack()">العودة</button>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Pending Orders -->
            <section class="pending-orders" id="pendingOrders">
                <h2>الطلبات المعلقة</h2>
                <div class="pending-list" id="pendingList">
                    <!-- Pending orders will appear here -->
                </div>
                <button class="refresh-btn" onclick="loadPendingOrders()">تحديث</button>
            </section>

            <!-- Order History -->
            <section class="order-history" id="orderHistory" style="display: none;">
                <h2>الطلبات القديمة</h2>
                <div class="history-list" id="historyList">
                    <!-- Order history will appear here -->
                </div>
                <button class="refresh-btn" onclick="loadOrderHistory()">تحديث</button>
            </section>

        </div>
    </main>

    <!-- Quantity Modal -->
    <div class="modal" id="quantityModal">
        <div class="modal-content">
            <h3>اختر الكمية</h3>
            <div class="quantity-selector">
                <button class="quantity-btn" onclick="setQuantity(0.25)">¼</button>
                <button class="quantity-btn" onclick="setQuantity(0.5)">½</button>
                <button class="quantity-btn" onclick="setQuantity(1)">1</button>
                <button class="quantity-btn" onclick="setQuantity(2)">2</button>
                <button class="quantity-btn" onclick="setQuantity(3)">3</button>
                <button class="quantity-btn" onclick="setQuantity(4)">4</button>
                <button class="quantity-btn" onclick="setQuantity(5)">5</button>
            </div>
            <div class="modal-actions">
                <button class="confirm-btn" onclick="addItemToOrder()">إضافة</button>
                <button class="cancel-btn" onclick="closeQuantityModal()">إلغاء</button>
            </div>
        </div>
    </div>

    <!-- Bottom Navigation -->
    <nav class="bottom-nav">
        <div class="nav-item active" onclick="showMainSections()">
            <span class="nav-icon">🏠</span>
            <span class="nav-text">الرئيسية</span>
        </div>
        <div class="nav-item" onclick="showSection('pendingOrders')">
            <span class="nav-icon">📋</span>
            <span class="nav-text">المعلقة</span>
            <span id="pendingBadge" class="notification-badge" style="display: none;">0</span>
        </div>
        <div class="nav-item" onclick="showSection('orderHistory')">
            <span class="nav-icon">📜</span>
            <span class="nav-text">القديمة</span>
        </div>
    </nav>

    <!-- Notification Toast -->
    <div class="toast" id="toast"></div>

    <!-- Scripts -->
    <script src="/socket.io/socket.io.js"></script>
    <script src="script.js"></script>
</body>
</html>
