from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                             QLineEdit, QPushButton, QMessageBox, QFrame)
from PyQt5.QtCore import Qt, QThread, pyqtSignal
from PyQt5.QtGui import <PERSON>Font, QPixmap

class LoginThread(QThread):
    """Thread for handling login API call"""
    login_result = pyqtSignal(dict)
    
    def __init__(self, api_client, username, password):
        super().__init__()
        self.api_client = api_client
        self.username = username
        self.password = password
    
    def run(self):
        result = self.api_client.login(self.username, self.password)
        self.login_result.emit(result)

class LoginWindow(QWidget):
    def __init__(self, main_app):
        super().__init__()
        self.main_app = main_app
        self.setup_ui()
    
    def setup_ui(self):
        """Set up the login UI"""
        layout = QVBoxLayout()
        layout.setAlignment(Qt.AlignCenter)
        layout.setSpacing(20)
        
        # Main container
        container = QFrame()
        container.setFixedSize(400, 500)
        container.setStyleSheet("""
            QFrame {
                background-color: white;
                border-radius: 10px;
                border: 2px solid #ddd;
            }
        """)
        
        container_layout = QVBoxLayout(container)
        container_layout.setSpacing(30)
        container_layout.setContentsMargins(40, 40, 40, 40)
        
        # Title
        title = QLabel("نظام فوترة المطعم")
        title.setAlignment(Qt.AlignCenter)
        title.setFont(QFont("Arial", 24, QFont.Bold))
        title.setStyleSheet("color: #1E2A38; margin-bottom: 10px;")
        container_layout.addWidget(title)
        
        # Subtitle
        subtitle = QLabel("مطعم النوفوس")
        subtitle.setAlignment(Qt.AlignCenter)
        subtitle.setFont(QFont("Arial", 16))
        subtitle.setStyleSheet("color: #D4AF37; margin-bottom: 30px;")
        container_layout.addWidget(subtitle)
        
        # Username field
        username_label = QLabel("اسم المستخدم:")
        username_label.setFont(QFont("Arial", 12, QFont.Bold))
        container_layout.addWidget(username_label)
        
        self.username_input = QLineEdit()
        self.username_input.setPlaceholderText("أدخل اسم المستخدم")
        self.username_input.setFont(QFont("Arial", 12))
        self.username_input.setLayoutDirection(Qt.LeftToRight)  # Fix text input direction
        self.username_input.setAlignment(Qt.AlignLeft)
        self.username_input.setStyleSheet("""
            QLineEdit {
                padding: 12px;
                border: 2px solid #ddd;
                border-radius: 8px;
                font-size: 14px;
                background-color: white;
                color: black;
                text-align: left;
            }
            QLineEdit:focus {
                border-color: #1E2A38;
                background-color: #f8f9fa;
            }
        """)
        container_layout.addWidget(self.username_input)
        
        # Password field
        password_label = QLabel("كلمة المرور:")
        password_label.setFont(QFont("Arial", 12, QFont.Bold))
        container_layout.addWidget(password_label)
        
        self.password_input = QLineEdit()
        self.password_input.setPlaceholderText("أدخل كلمة المرور")
        self.password_input.setEchoMode(QLineEdit.Password)
        self.password_input.setFont(QFont("Arial", 12))
        self.password_input.setLayoutDirection(Qt.LeftToRight)  # Fix text input direction
        self.password_input.setAlignment(Qt.AlignLeft)
        self.password_input.setStyleSheet("""
            QLineEdit {
                padding: 12px;
                border: 2px solid #ddd;
                border-radius: 8px;
                font-size: 14px;
                background-color: white;
                color: black;
                text-align: left;
            }
            QLineEdit:focus {
                border-color: #1E2A38;
                background-color: #f8f9fa;
            }
        """)
        container_layout.addWidget(self.password_input)
        
        # Login button
        self.login_button = QPushButton("تسجيل دخول")
        self.login_button.setFont(QFont("Arial", 14, QFont.Bold))
        self.login_button.setFixedHeight(50)
        self.login_button.setStyleSheet("""
            QPushButton {
                background-color: #1E2A38;
                color: white;
                border: none;
                border-radius: 8px;
                font-size: 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #D4AF37;
            }
            QPushButton:pressed {
                background-color: #B8941F;
            }
            QPushButton:disabled {
                background-color: #cccccc;
                color: #666666;
            }
        """)
        self.login_button.clicked.connect(self.handle_login)
        container_layout.addWidget(self.login_button)
        
        # Status label
        self.status_label = QLabel("")
        self.status_label.setAlignment(Qt.AlignCenter)
        self.status_label.setFont(QFont("Arial", 10))
        self.status_label.setStyleSheet("color: #666;")
        container_layout.addWidget(self.status_label)
        
        # Add container to main layout
        layout.addWidget(container, alignment=Qt.AlignCenter)
        self.setLayout(layout)
        
        # Connect Enter key to login
        self.username_input.returnPressed.connect(self.handle_login)
        self.password_input.returnPressed.connect(self.handle_login)
        
        # Set default values for testing
        self.username_input.setText("admin")
        self.password_input.setText("password")
    
    def handle_login(self):
        """Handle login button click"""
        username = self.username_input.text().strip()
        password = self.password_input.text().strip()
        
        if not username or not password:
            self.show_message("خطأ", "يرجى إدخال اسم المستخدم وكلمة المرور", QMessageBox.Warning)
            return
        
        # Disable login button and show loading
        self.login_button.setEnabled(False)
        self.login_button.setText("جاري تسجيل الدخول...")
        self.status_label.setText("جاري الاتصال بالخادم...")
        
        # Start login thread
        self.login_thread = LoginThread(self.main_app.api_client, username, password)
        self.login_thread.login_result.connect(self.handle_login_result)
        self.login_thread.start()
    
    def handle_login_result(self, result):
        """Handle login API result"""
        # Re-enable login button
        self.login_button.setEnabled(True)
        self.login_button.setText("تسجيل دخول")
        self.status_label.setText("")
        
        if 'error' in result:
            self.show_message("خطأ في تسجيل الدخول", result['error'], QMessageBox.Critical)
        elif 'user' in result and 'token' in result:
            # Login successful
            self.main_app.login_success(result['user'], result['token'])
            self.clear_form()
        else:
            self.show_message("خطأ", "حدث خطأ غير متوقع", QMessageBox.Critical)
    
    def clear_form(self):
        """Clear login form"""
        self.username_input.clear()
        self.password_input.clear()
        self.username_input.setFocus()
    
    def show_message(self, title, message, icon=QMessageBox.Information):
        """Show message box"""
        msg_box = QMessageBox()
        msg_box.setWindowTitle(title)
        msg_box.setText(message)
        msg_box.setIcon(icon)
        msg_box.exec_()
    
    def showEvent(self, event):
        """Called when window is shown"""
        super().showEvent(event)
        self.username_input.setFocus()
