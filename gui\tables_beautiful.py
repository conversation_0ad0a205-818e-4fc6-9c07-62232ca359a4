#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام إدارة مطعم النوفوس - شاشة الطاولات الجميلة
Restaurant Management System - Beautiful Tables Screen

تطوير: شركة الباش البرمجية
Developer: Al-Bash Software Company
الهاتف/Phone: 07715086335
واتساب/WhatsApp: 07715086335
البريد الإلكتروني/Email: <EMAIL>
الموقع/Website: www.albash-software.com

© 2024 شركة الباش البرمجية. جميع الحقوق محفوظة.
© 2024 Al-Bash Software Company. All rights reserved.
"""

from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *
import json
import os

class BeautifulTablesWindow(QWidget):
    def __init__(self, main_app):
        super().__init__()
        self.main_app = main_app
        self.current_hall = 1
        self.halls_data = []
        self.load_halls_data()
        self.setup_ui()
        self.setup_animations()
    
    def setup_ui(self):
        """Set up the beautiful tables UI"""
        layout = QVBoxLayout()
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)
        
        # Set beautiful background
        self.setStyleSheet("""
            QWidget {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #1E2A38, stop:0.3 #2C3E50, stop:0.7 #34495E, stop:1 #D4AF37);
            }
        """)
        
        # Create beautiful header
        self.create_beautiful_header(layout)
        
        # Create hall selection
        self.create_hall_selection(layout)
        
        # Create tables grid
        self.create_tables_grid(layout)
        
        # Create beautiful footer
        self.create_beautiful_footer(layout)
        
        self.setLayout(layout)
    
    def create_beautiful_header(self, parent_layout):
        """Create beautiful header"""
        header_frame = QFrame()
        header_frame.setFixedHeight(80)
        header_frame.setStyleSheet("""
            QFrame {
                background: rgba(255, 255, 255, 0.15);
                border-bottom: 3px solid #D4AF37;
            }
        """)
        
        header_layout = QHBoxLayout(header_frame)
        header_layout.setContentsMargins(30, 20, 30, 20)
        
        # Back button
        back_btn = QPushButton("🔙")
        back_btn.setFixedSize(50, 50)
        back_btn.setStyleSheet("""
            QPushButton {
                background: rgba(255, 255, 255, 0.2);
                color: #D4AF37;
                border: 2px solid #D4AF37;
                border-radius: 25px;
                font-size: 20px;
            }
            QPushButton:hover {
                background: #D4AF37;
                color: #1E2A38;
            }
        """)
        back_btn.clicked.connect(self.go_back)
        header_layout.addWidget(back_btn)
        
        # Title
        title_label = QLabel("🪑 إدارة الطاولات")
        title_label.setFont(QFont("Arial", 24, QFont.Bold))
        title_label.setStyleSheet("""
            QLabel {
                color: white;
                text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
                margin-left: 20px;
            }
        """)
        header_layout.addWidget(title_label)
        
        header_layout.addStretch()
        
        parent_layout.addWidget(header_frame)
    
    def create_hall_selection(self, parent_layout):
        """Create beautiful hall selection"""
        hall_frame = QFrame()
        hall_frame.setFixedHeight(100)
        hall_frame.setStyleSheet("""
            QFrame {
                background: rgba(255, 255, 255, 0.1);
                border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            }
        """)
        
        hall_layout = QVBoxLayout(hall_frame)
        hall_layout.setContentsMargins(30, 20, 30, 20)
        hall_layout.setSpacing(15)
        
        # Hall selection label
        hall_label = QLabel("اختر القاعة:")
        hall_label.setFont(QFont("Arial", 16, QFont.Bold))
        hall_label.setStyleSheet("""
            QLabel {
                color: white;
                text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
            }
        """)
        hall_layout.addWidget(hall_label)
        
        # Hall buttons
        self.create_hall_buttons(hall_layout)
        
        parent_layout.addWidget(hall_frame)
    
    def create_hall_buttons(self, parent_layout):
        """Create beautiful hall selection buttons"""
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(20)
        
        self.hall_buttons = []
        
        for hall in self.halls_data:
            btn = QPushButton(f"🏛️ {hall['name']}")
            btn.setFixedSize(200, 40)
            btn.setFont(QFont("Arial", 12, QFont.Bold))
            btn.setProperty("hall_id", hall['id'])
            btn.clicked.connect(lambda checked, hall_id=hall['id']: self.select_hall(hall_id))
            
            # Style for hall buttons
            btn.setStyleSheet("""
                QPushButton {
                    background: rgba(255, 255, 255, 0.2);
                    color: white;
                    border: 2px solid rgba(255, 255, 255, 0.3);
                    border-radius: 20px;
                    padding: 8px 15px;
                    text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
                }
                QPushButton:hover {
                    background: rgba(212, 175, 55, 0.6);
                    border: 2px solid #D4AF37;
                }
                QPushButton[selected="true"] {
                    background: #D4AF37;
                    color: #1E2A38;
                    border: 2px solid #F1C40F;
                    text-shadow: 1px 1px 2px rgba(255,255,255,0.3);
                }
            """)
            
            self.hall_buttons.append(btn)
            buttons_layout.addWidget(btn)
        
        buttons_layout.addStretch()
        parent_layout.addLayout(buttons_layout)
        
        # Select first hall by default
        if self.hall_buttons:
            self.select_hall(self.halls_data[0]['id'])
    
    def create_tables_grid(self, parent_layout):
        """Create beautiful tables grid"""
        # Scroll area for tables
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setStyleSheet("""
            QScrollArea {
                background: transparent;
                border: none;
            }
            QScrollBar:vertical {
                background: rgba(255, 255, 255, 0.1);
                width: 12px;
                border-radius: 6px;
            }
            QScrollBar::handle:vertical {
                background: #D4AF37;
                border-radius: 6px;
                min-height: 20px;
            }
            QScrollBar::handle:vertical:hover {
                background: #F1C40F;
            }
        """)
        
        # Tables container
        self.tables_container = QWidget()
        self.tables_layout = QGridLayout(self.tables_container)
        self.tables_layout.setSpacing(20)
        self.tables_layout.setContentsMargins(30, 30, 30, 30)
        
        scroll_area.setWidget(self.tables_container)
        parent_layout.addWidget(scroll_area)
        
        # Load tables for current hall
        self.update_tables_display()
    
    def create_table_button(self, table_number):
        """Create beautiful table button"""
        btn = QPushButton()
        btn.setFixedSize(150, 120)
        btn.clicked.connect(lambda: self.open_table_order(table_number))
        
        # Create button content
        btn_layout = QVBoxLayout()
        btn_layout.setAlignment(Qt.AlignCenter)
        btn_layout.setSpacing(10)
        
        # Table icon
        icon_label = QLabel("🪑")
        icon_label.setFont(QFont("Arial", 32))
        icon_label.setAlignment(Qt.AlignCenter)
        icon_label.setStyleSheet("color: white; text-shadow: 2px 2px 4px rgba(0,0,0,0.5);")
        
        # Table number
        number_label = QLabel(f"طاولة {table_number}")
        number_label.setFont(QFont("Arial", 14, QFont.Bold))
        number_label.setAlignment(Qt.AlignCenter)
        number_label.setStyleSheet("color: white; text-shadow: 1px 1px 2px rgba(0,0,0,0.5);")
        
        # Status (you can add logic to check if table is occupied)
        status_label = QLabel("متاحة")
        status_label.setFont(QFont("Arial", 10))
        status_label.setAlignment(Qt.AlignCenter)
        status_label.setStyleSheet("color: #D4AF37; text-shadow: 1px 1px 2px rgba(0,0,0,0.5);")
        
        btn.setText(f"🪑\nطاولة {table_number}\nمتاحة")
        btn.setFont(QFont("Arial", 12, QFont.Bold))
        
        btn.setStyleSheet("""
            QPushButton {
                background: rgba(255, 255, 255, 0.15);
                color: white;
                border: 3px solid rgba(255, 255, 255, 0.3);
                border-radius: 15px;
                padding: 15px;
                text-align: center;
                text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
            }
            QPushButton:hover {
                background: rgba(212, 175, 55, 0.8);
                border: 3px solid #D4AF37;
                color: #1E2A38;
                transform: scale(1.05);
                text-shadow: 1px 1px 2px rgba(255,255,255,0.3);
            }
            QPushButton:pressed {
                background: rgba(184, 148, 31, 0.9);
                transform: scale(0.98);
            }
        """)
        
        # Add shadow effect
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(15)
        shadow.setColor(QColor(0, 0, 0, 60))
        shadow.setOffset(0, 8)
        btn.setGraphicsEffect(shadow)
        
        return btn
    
    def create_beautiful_footer(self, parent_layout):
        """Create beautiful footer"""
        footer_frame = QFrame()
        footer_frame.setFixedHeight(60)
        footer_frame.setStyleSheet("""
            QFrame {
                background: rgba(255, 255, 255, 0.1);
                border-top: 2px solid rgba(255, 255, 255, 0.2);
            }
        """)
        
        footer_layout = QHBoxLayout(footer_frame)
        footer_layout.setContentsMargins(30, 15, 30, 15)
        
        # Info label
        info_label = QLabel("انقر على الطاولة لبدء طلب جديد")
        info_label.setFont(QFont("Arial", 12))
        info_label.setStyleSheet("""
            QLabel {
                color: white;
                text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
            }
        """)
        footer_layout.addWidget(info_label)
        
        footer_layout.addStretch()
        
        # Company info
        company_label = QLabel("شركة الباش البرمجية")
        company_label.setFont(QFont("Arial", 10))
        company_label.setStyleSheet("""
            QLabel {
                color: #D4AF37;
                text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
            }
        """)
        footer_layout.addWidget(company_label)
        
        parent_layout.addWidget(footer_frame)
    
    def setup_animations(self):
        """Setup beautiful animations"""
        self.fade_animation = QPropertyAnimation(self, b"windowOpacity")
        self.fade_animation.setDuration(800)
        self.fade_animation.setStartValue(0.0)
        self.fade_animation.setEndValue(1.0)
        self.fade_animation.setEasingCurve(QEasingCurve.OutCubic)
    
    def load_halls_data(self):
        """Load halls data from file"""
        try:
            if os.path.exists('data/halls.json'):
                with open('data/halls.json', 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self.halls_data = data.get('halls', [])
            else:
                # Default halls data
                self.halls_data = [
                    {"id": 1, "name": "القاعة الرئيسية", "tables": 12},
                    {"id": 2, "name": "القاعة الثانية", "tables": 8},
                    {"id": 3, "name": "القاعة الخارجية", "tables": 6}
                ]
        except Exception as e:
            print(f"Error loading halls data: {e}")
            self.halls_data = [
                {"id": 1, "name": "القاعة الرئيسية", "tables": 12}
            ]
    
    def select_hall(self, hall_id):
        """Select hall and update display"""
        self.current_hall = hall_id
        
        # Update button styles
        for btn in self.hall_buttons:
            if btn.property("hall_id") == hall_id:
                btn.setProperty("selected", "true")
            else:
                btn.setProperty("selected", "false")
            btn.style().unpolish(btn)
            btn.style().polish(btn)
        
        # Update tables display
        self.update_tables_display()
    
    def update_tables_display(self):
        """Update tables display for current hall"""
        # Clear existing tables
        for i in reversed(range(self.tables_layout.count())):
            self.tables_layout.itemAt(i).widget().setParent(None)
        
        # Find current hall
        current_hall_data = None
        for hall in self.halls_data:
            if hall['id'] == self.current_hall:
                current_hall_data = hall
                break
        
        if not current_hall_data:
            return
        
        # Create table buttons
        tables_count = current_hall_data['tables']
        columns = 4  # 4 tables per row
        
        for i in range(tables_count):
            table_number = i + 1
            row = i // columns
            col = i % columns
            
            table_btn = self.create_table_button(table_number)
            self.tables_layout.addWidget(table_btn, row, col)
    
    def open_table_order(self, table_number):
        """Open order for specific table"""
        # Set table info in main app
        self.main_app.current_order_type = 'dine-in'
        self.main_app.current_hall_id = self.current_hall
        self.main_app.current_table_number = table_number
        
        # Show menu
        self.main_app.show_menu('dine-in', hall_id=self.current_hall, table_number=table_number)
    
    def go_back(self):
        """Go back to dashboard"""
        self.main_app.show_dashboard()
    
    def showEvent(self, event):
        """Override show event to start animations"""
        super().showEvent(event)
        self.fade_animation.start()
    
    def load_waiter_order(self, order):
        """Load waiter order for editing"""
        try:
            # Set hall and table based on order
            if order.get('hallId') and order.get('tableNumber'):
                self.current_hall = order.get('hallId')
                table_number = order.get('tableNumber')
                
                # Update hall selection
                self.select_hall(self.current_hall)
                
                # Open order window for the specific table
                self.open_table_order(table_number)
            else:
                QMessageBox.critical(self, "خطأ", "معلومات الطاولة غير مكتملة في الطلب")
                
        except Exception as e:
            print(f"Error loading waiter order: {e}")
            QMessageBox.critical(self, "خطأ", f"خطأ في تحميل طلب النادل: {str(e)}")
