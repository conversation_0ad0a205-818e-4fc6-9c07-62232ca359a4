// Global variables
let socket;
let currentOrderType = null;
let currentHallId = null;
let currentTableNumber = null;
let menuData = null;
let hallsData = null;
let currentOrder = [];
let currentOrderId = null;
let selectedItem = null;
let selectedQuantity = 1;
let waiterName = 'النادل';

// Initialize app
document.addEventListener('DOMContentLoaded', function() {
    initializeSocket();
    loadWaiterName();
    loadPendingOrders();
    
    // Set waiter name
    document.getElementById('waiterName').textContent = waiterName;
});

// Socket.io initialization
function initializeSocket() {
    socket = io();
    
    socket.on('connect', function() {
        console.log('Connected to server');
        showToast('متصل بالخادم', 'success');
    });
    
    socket.on('disconnect', function() {
        console.log('Disconnected from server');
        showToast('انقطع الاتصال بالخادم', 'error');
    });
    
    socket.on('order_approved', function(data) {
        showToast(`تم الموافقة على الطلب #${data.orderNumber}`, 'success');
        loadPendingOrders();
        updateNotificationCount();
    });
    
    socket.on('order_rejected', function(data) {
        showToast(`تم رفض الطلب #${data.orderNumber}`, 'error');
        loadPendingOrders();
        updateNotificationCount();
    });
}

// Load waiter name from localStorage
function loadWaiterName() {
    const savedName = localStorage.getItem('waiterName');
    if (savedName) {
        waiterName = savedName;
    } else {
        const name = prompt('أدخل اسمك:');
        if (name && name.trim()) {
            waiterName = name.trim();
            localStorage.setItem('waiterName', waiterName);
        }
    }
}

// Order type selection
function selectOrderType(type) {
    currentOrderType = type;
    
    if (type === 'dine-in') {
        loadHallsAndTables();
        showSection('tableSelection');
    } else if (type === 'delivery') {
        showSection('deliveryInfo');
    } else if (type === 'takeaway') {
        loadMenu();
        showSection('menuSection');
        updateOrderInfo();
    }
}

// Load halls and tables
async function loadHallsAndTables() {
    try {
        const response = await fetch('/api/halls');
        hallsData = await response.json();
        
        const container = document.getElementById('hallsContainer');
        container.innerHTML = '';
        
        hallsData.halls.forEach(hall => {
            const hallDiv = document.createElement('div');
            hallDiv.className = 'hall';
            
            const hallTitle = document.createElement('h3');
            hallTitle.textContent = hall.name;
            hallDiv.appendChild(hallTitle);
            
            const tablesGrid = document.createElement('div');
            tablesGrid.className = 'tables-grid';
            
            for (let i = 1; i <= hall.tableCount; i++) {
                const tableBtn = document.createElement('button');
                tableBtn.className = 'table-btn';
                tableBtn.textContent = i;
                tableBtn.onclick = () => selectTable(hall.id, i);
                tablesGrid.appendChild(tableBtn);
            }
            
            hallDiv.appendChild(tablesGrid);
            container.appendChild(hallDiv);
        });
        
    } catch (error) {
        console.error('Error loading halls:', error);
        showToast('خطأ في تحميل القاعات', 'error');
    }
}

// Select table
function selectTable(hallId, tableNumber) {
    currentHallId = hallId;
    currentTableNumber = tableNumber;
    
    loadMenu();
    showSection('menuSection');
    updateOrderInfo();
}

// Continue to menu (for delivery)
function continueToMenu() {
    const phone = document.getElementById('customerPhone').value.trim();
    const address = document.getElementById('customerAddress').value.trim();
    
    if (!phone || !address) {
        showToast('يرجى إدخال رقم الهاتف والعنوان', 'warning');
        return;
    }
    
    loadMenu();
    showSection('menuSection');
    updateOrderInfo();
}

// Load menu
async function loadMenu() {
    try {
        console.log('Loading menu...');
        const response = await fetch('/api/menu');
        menuData = await response.json();

        console.log('Menu data received:', menuData);
        console.log('Number of sections:', menuData.sections ? menuData.sections.length : 0);

        displayCategories();

    } catch (error) {
        console.error('Error loading menu:', error);
        showToast('خطأ في تحميل القائمة', 'error');
    }
}

// Display categories
function displayCategories() {
    console.log('Displaying categories...');
    const container = document.getElementById('categoriesList');

    if (!container) {
        console.error('Categories container not found!');
        return;
    }

    if (!menuData || !menuData.sections) {
        console.error('No menu data or sections!');
        return;
    }

    console.log('Categories container found, clearing...');
    container.innerHTML = '';

    console.log('Processing sections:', menuData.sections);
    menuData.sections.forEach((section, index) => {
        console.log(`Creating category ${index}: ${section.name}`);
        const categoryDiv = document.createElement('div');
        categoryDiv.className = 'category-item';
        categoryDiv.textContent = section.name;
        categoryDiv.onclick = () => selectCategory(index);

        if (index === 0) {
            categoryDiv.classList.add('active');
        }

        container.appendChild(categoryDiv);
        console.log(`Category ${index} added to container`);
    });

    console.log('All categories displayed');

    // Select first category after all categories are added
    if (menuData.sections.length > 0) {
        console.log('Selecting first category...');
        selectCategory(0);
    }
}

// Select category
function selectCategory(index) {
    console.log(`Selecting category ${index}`);

    if (!menuData || !menuData.sections || !menuData.sections[index]) {
        console.error(`Invalid category index: ${index}`);
        return;
    }

    // Update active category
    const categoryItems = document.querySelectorAll('.category-item');
    console.log(`Found ${categoryItems.length} category items`);

    categoryItems.forEach(item => {
        item.classList.remove('active');
    });

    if (categoryItems[index]) {
        categoryItems[index].classList.add('active');
        console.log(`Category ${index} marked as active`);
    } else {
        console.error(`Category item ${index} not found`);
    }

    // Display items
    const items = menuData.sections[index].items || [];
    console.log(`Displaying ${items.length} items for category ${index}`);
    displayItems(items);
}

// Display items
function displayItems(items) {
    console.log('Displaying items:', items);
    const container = document.getElementById('itemsList');

    if (!container) {
        console.error('Items container not found!');
        return;
    }

    console.log('Items container found, clearing...');
    container.innerHTML = '';

    if (!items || items.length === 0) {
        console.log('No items to display');
        container.innerHTML = '<p style="text-align: center; color: #666;">لا توجد أصناف في هذا القسم</p>';
        return;
    }

    console.log(`Processing ${items.length} items`);
    items.forEach((item, index) => {
        console.log(`Creating item ${index}: ${item.name} - ${item.price}`);
        const itemDiv = document.createElement('div');
        itemDiv.className = 'menu-item';
        itemDiv.onclick = () => openQuantityModal(item);

        const nameSpan = document.createElement('span');
        nameSpan.className = 'item-name';
        nameSpan.textContent = item.name;

        const priceSpan = document.createElement('span');
        priceSpan.className = 'item-price';
        const formattedPrice = Math.floor(item.price).toLocaleString().replace(/,/g, '.');
        priceSpan.textContent = `${formattedPrice} دينار`;

        itemDiv.appendChild(nameSpan);
        itemDiv.appendChild(priceSpan);
        container.appendChild(itemDiv);
        console.log(`Item ${index} added to container`);
    });

    console.log('All items displayed');
}

// Open quantity modal
function openQuantityModal(item) {
    selectedItem = item;
    selectedQuantity = 1;
    
    // Reset quantity buttons
    document.querySelectorAll('.quantity-btn').forEach(btn => {
        btn.classList.remove('selected');
    });
    document.querySelector('.quantity-btn[onclick="setQuantity(1)"]').classList.add('selected');
    
    document.getElementById('quantityModal').style.display = 'block';
}

// Set quantity
function setQuantity(quantity) {
    selectedQuantity = quantity;
    
    // Update selected button
    document.querySelectorAll('.quantity-btn').forEach(btn => {
        btn.classList.remove('selected');
    });
    event.target.classList.add('selected');
}

// Close quantity modal
function closeQuantityModal() {
    document.getElementById('quantityModal').style.display = 'none';
    selectedItem = null;
    selectedQuantity = 1;
}

// Add item to order
function addItemToOrder() {
    if (!selectedItem) return;
    
    // Check if item already exists in order
    const existingIndex = currentOrder.findIndex(orderItem => 
        orderItem.id === selectedItem.id
    );
    
    if (existingIndex >= 0) {
        // Update quantity
        currentOrder[existingIndex].quantity += selectedQuantity;
    } else {
        // Add new item
        currentOrder.push({
            id: selectedItem.id,
            name: selectedItem.name,
            price: selectedItem.price,
            quantity: selectedQuantity
        });
    }
    
    updateOrderSummary();
    closeQuantityModal();
    showToast(`تم إضافة ${selectedItem.name}`, 'success');
}

// Update order summary
function updateOrderSummary() {
    const container = document.getElementById('orderItems');
    const totalElement = document.getElementById('orderTotal');
    
    container.innerHTML = '';
    let total = 0;
    
    currentOrder.forEach((item, index) => {
        const itemDiv = document.createElement('div');
        itemDiv.className = 'order-item';
        
        const infoDiv = document.createElement('div');
        infoDiv.className = 'order-item-info';
        
        const nameDiv = document.createElement('div');
        nameDiv.className = 'order-item-name';
        nameDiv.textContent = item.name;
        
        const detailsDiv = document.createElement('div');
        detailsDiv.className = 'order-item-details';
        
        // Format quantity
        let qtyText = item.quantity.toString();
        if (item.quantity === 0.25) qtyText = '¼';
        else if (item.quantity === 0.5) qtyText = '½';
        
        const itemTotal = item.price * item.quantity;
        const formattedPrice = Math.floor(item.price).toLocaleString().replace(/,/g, '.');
        const formattedTotal = Math.floor(itemTotal).toLocaleString().replace(/,/g, '.');
        
        detailsDiv.textContent = `${qtyText} × ${formattedPrice} = ${formattedTotal} دينار`;
        
        infoDiv.appendChild(nameDiv);
        infoDiv.appendChild(detailsDiv);
        
        const removeBtn = document.createElement('button');
        removeBtn.className = 'remove-item-btn';
        removeBtn.innerHTML = '🗑️';
        removeBtn.onclick = () => removeItemFromOrder(index);
        
        itemDiv.appendChild(infoDiv);
        itemDiv.appendChild(removeBtn);
        container.appendChild(itemDiv);
        
        total += itemTotal;
    });
    
    const formattedTotal = Math.floor(total).toLocaleString().replace(/,/g, '.');
    totalElement.textContent = formattedTotal;
}

// Remove item from order
function removeItemFromOrder(index) {
    currentOrder.splice(index, 1);
    updateOrderSummary();
    showToast('تم حذف الصنف', 'success');
}

// Clear order
function clearOrder() {
    if (currentOrder.length === 0) return;
    
    if (confirm('هل تريد مسح الطلب؟')) {
        currentOrder = [];
        updateOrderSummary();
        showToast('تم مسح الطلب', 'success');
    }
}

// Send order
async function sendOrder() {
    if (currentOrder.length === 0) {
        showToast('الطلب فارغ', 'warning');
        return;
    }
    
    const notes = document.getElementById('orderNotes').value.trim();
    
    const orderData = {
        type: currentOrderType,
        hallId: currentHallId,
        tableNumber: currentTableNumber,
        customerPhone: currentOrderType === 'delivery' ? document.getElementById('customerPhone').value : null,
        customerAddress: currentOrderType === 'delivery' ? document.getElementById('customerAddress').value : null,
        items: currentOrder,
        notes: notes,
        waiterName: waiterName
    };
    
    try {
        const response = await fetch('/api/waiter-orders', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(orderData)
        });
        
        const result = await response.json();
        
        if (result.success) {
            showToast('تم إرسال الطلب للكاشير', 'success');

            // حفظ معرف الطلب للتعديل
            currentOrderId = result.orderId;

            // إظهار أزرار التعديل والإلغاء
            showOrderActions();

            loadPendingOrders();
            updateNotificationCount();
        } else {
            showToast(result.error || 'فشل في إرسال الطلب', 'error');
        }
        
    } catch (error) {
        console.error('Error sending order:', error);
        showToast('خطأ في إرسال الطلب', 'error');
    }
}

// Show order actions (edit/cancel)
function showOrderActions() {
    // إخفاء زر الإرسال
    const sendBtn = document.querySelector('.send-order-btn');
    if (sendBtn) {
        sendBtn.style.display = 'none';
    }

    // إنشاء أزرار الإجراءات
    const actionsDiv = document.createElement('div');
    actionsDiv.id = 'orderActions';
    actionsDiv.className = 'order-actions';
    actionsDiv.innerHTML = `
        <div class="action-buttons">
            <button onclick="editOrder()" class="edit-btn">تعديل الطلب</button>
            <button onclick="cancelOrder()" class="cancel-btn">إلغاء الطلب</button>
            <button onclick="newOrder()" class="new-btn">طلب جديد</button>
        </div>
        <p class="order-status">الطلب في انتظار موافقة الكاشير...</p>
    `;

    // إضافة الأزرار بعد ملخص الطلب
    const orderSummary = document.querySelector('.order-summary');
    if (orderSummary) {
        orderSummary.appendChild(actionsDiv);
    }
}

// Edit current order
function editOrder() {
    showToast('يمكنك الآن تعديل الطلب', 'info');

    // إخفاء أزرار الإجراءات
    const actionsDiv = document.getElementById('orderActions');
    if (actionsDiv) {
        actionsDiv.remove();
    }

    // إظهار زر الإرسال مرة أخرى
    const sendBtn = document.querySelector('.send-order-btn');
    if (sendBtn) {
        sendBtn.style.display = 'block';
        sendBtn.textContent = 'تحديث الطلب';
        sendBtn.onclick = updateOrder; // تغيير الوظيفة للتحديث
    }
}

// Update existing order
async function updateOrder() {
    if (currentOrder.length === 0) {
        showToast('الطلب فارغ', 'warning');
        return;
    }

    const notes = document.getElementById('orderNotes').value.trim();

    const orderData = {
        type: currentOrderType,
        hallId: currentHallId,
        tableNumber: currentTableNumber,
        customerPhone: currentOrderType === 'delivery' ? document.getElementById('customerPhone').value : null,
        customerAddress: currentOrderType === 'delivery' ? document.getElementById('customerAddress').value : null,
        items: currentOrder,
        notes: notes,
        waiterName: waiterName
    };

    try {
        const response = await fetch(`/api/waiter-orders/${currentOrderId}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(orderData)
        });

        const result = await response.json();

        if (result.success) {
            showToast('تم تحديث الطلب بنجاح', 'success');
            showOrderActions(); // إظهار أزرار الإجراءات مرة أخرى
            loadPendingOrders();
        } else {
            showToast(result.error || 'فشل في تحديث الطلب', 'error');
        }

    } catch (error) {
        console.error('Error updating order:', error);
        showToast('خطأ في تحديث الطلب', 'error');
    }
}

// Cancel order
async function cancelOrder() {
    if (!currentOrderId) {
        showToast('لا يوجد طلب للإلغاء', 'error');
        return;
    }

    if (!confirm('هل أنت متأكد من إلغاء الطلب؟')) {
        return;
    }

    try {
        const response = await fetch(`/api/waiter-orders/${currentOrderId}`, {
            method: 'DELETE'
        });

        const result = await response.json();

        if (result.success) {
            showToast('تم إلغاء الطلب', 'success');
            newOrder();
        } else {
            showToast(result.error || 'فشل في إلغاء الطلب', 'error');
        }
    } catch (error) {
        console.error('Error canceling order:', error);
        showToast('خطأ في إلغاء الطلب', 'error');
    }
}

// Start new order
function newOrder() {
    currentOrder = [];
    currentOrderId = null;
    currentOrderType = null;
    currentHallId = null;
    currentTableNumber = null;

    // إزالة أزرار الإجراءات
    const actionsDiv = document.getElementById('orderActions');
    if (actionsDiv) {
        actionsDiv.remove();
    }

    // إعادة تعيين زر الإرسال
    const sendBtn = document.querySelector('.send-order-btn');
    if (sendBtn) {
        sendBtn.style.display = 'block';
        sendBtn.textContent = 'إرسال الطلب';
        sendBtn.onclick = sendOrder;
    }

    updateOrderSummary();
    showSection('orderTypeSection');
}

// Load pending orders
async function loadPendingOrders() {
    try {
        const response = await fetch('/api/waiter-orders/pending');
        const data = await response.json();
        
        const container = document.getElementById('pendingList');
        container.innerHTML = '';
        
        if (data.orders.length === 0) {
            container.innerHTML = '<p style="text-align: center; color: #666;">لا توجد طلبات معلقة</p>';
            return;
        }
        
        data.orders.forEach(order => {
            const orderDiv = document.createElement('div');
            orderDiv.className = `pending-item ${order.status}`;
            
            const headerDiv = document.createElement('div');
            headerDiv.className = 'pending-header';
            
            const numberSpan = document.createElement('span');
            numberSpan.className = 'pending-number';
            numberSpan.textContent = `طلب #${order.orderNumber}`;
            
            const statusSpan = document.createElement('span');
            statusSpan.className = `pending-status ${order.status}`;
            statusSpan.textContent = getStatusText(order.status);
            
            headerDiv.appendChild(numberSpan);
            headerDiv.appendChild(statusSpan);
            
            const detailsDiv = document.createElement('div');
            detailsDiv.innerHTML = `
                <p><strong>النوع:</strong> ${getOrderTypeText(order.type)}</p>
                <p><strong>المجموع:</strong> ${Math.floor(order.totalAmount).toLocaleString().replace(/,/g, '.')} دينار</p>
                <p><strong>الوقت:</strong> ${new Date(order.createdAt).toLocaleString('ar-IQ')}</p>
            `;
            
            orderDiv.appendChild(headerDiv);
            orderDiv.appendChild(detailsDiv);
            container.appendChild(orderDiv);
        });
        
    } catch (error) {
        console.error('Error loading pending orders:', error);
        showToast('خطأ في تحميل الطلبات المعلقة', 'error');
    }
}

// Helper functions
function getStatusText(status) {
    switch (status) {
        case 'pending_approval': return 'في انتظار الموافقة';
        case 'approved': return 'تم الموافقة';
        case 'rejected': return 'مرفوض';
        default: return status;
    }
}

function getOrderTypeText(type) {
    switch (type) {
        case 'dine-in': return 'طاولات';
        case 'takeaway': return 'سفري';
        case 'delivery': return 'دلفري';
        default: return type;
    }
}

function updateOrderInfo() {
    const infoElement = document.getElementById('orderInfo');
    
    if (currentOrderType === 'dine-in') {
        const hall = hallsData.halls.find(h => h.id === currentHallId);
        infoElement.textContent = `${hall.name} - طاولة ${currentTableNumber}`;
    } else if (currentOrderType === 'takeaway') {
        infoElement.textContent = 'طلب سفري';
    } else if (currentOrderType === 'delivery') {
        infoElement.textContent = 'طلب دلفري';
    }
}

function updateNotificationCount() {
    // This would be implemented to show notification count
    // For now, we'll just update it when orders change
}

function showSection(sectionId) {
    // Hide all sections
    document.querySelectorAll('.order-type-section, .table-selection, .delivery-info, .menu-section').forEach(section => {
        section.style.display = 'none';
    });
    
    // Show selected section
    document.getElementById(sectionId).style.display = 'block';
}

function goBack() {
    if (document.getElementById('menuSection').style.display === 'block') {
        if (currentOrderType === 'dine-in') {
            showSection('tableSelection');
        } else if (currentOrderType === 'delivery') {
            showSection('deliveryInfo');
        } else {
            showSection('orderTypeSection');
        }
    } else if (document.getElementById('tableSelection').style.display === 'block' || 
               document.getElementById('deliveryInfo').style.display === 'block') {
        showSection('orderTypeSection');
    }
    
    // Reset order type if going back to main
    if (document.getElementById('orderTypeSection').style.display === 'block') {
        currentOrderType = null;
        currentHallId = null;
        currentTableNumber = null;
    }
}

function showToast(message, type = 'info') {
    const toast = document.getElementById('toast');
    toast.textContent = message;
    toast.className = `toast ${type} show`;

    setTimeout(() => {
        toast.classList.remove('show');
    }, 3000);
}

// Load order history
async function loadOrderHistory() {
    try {
        const response = await fetch('/api/waiter-orders/history');
        const data = await response.json();

        const container = document.getElementById('historyList');
        container.innerHTML = '';

        if (data.orders.length === 0) {
            container.innerHTML = '<p style="text-align: center; color: #666;">لا توجد طلبات سابقة</p>';
            return;
        }

        data.orders.forEach(order => {
            const orderDiv = document.createElement('div');
            orderDiv.className = `history-item ${order.status}`;

            const headerDiv = document.createElement('div');
            headerDiv.className = 'history-header';

            const numberSpan = document.createElement('span');
            numberSpan.className = 'history-number';
            numberSpan.textContent = `طلب #${order.orderNumber}`;

            const statusSpan = document.createElement('span');
            statusSpan.className = `history-status ${order.status}`;
            statusSpan.textContent = getStatusText(order.status);

            headerDiv.appendChild(numberSpan);
            headerDiv.appendChild(statusSpan);

            const detailsDiv = document.createElement('div');
            detailsDiv.innerHTML = `
                <p><strong>النوع:</strong> ${getOrderTypeText(order.type)}</p>
                <p><strong>المجموع:</strong> ${Math.floor(order.totalAmount).toLocaleString().replace(/,/g, '.')} دينار</p>
                <p><strong>الوقت:</strong> ${new Date(order.createdAt).toLocaleString('ar-IQ')}</p>
            `;

            // Actions for approved orders only
            if (order.status === 'approved') {
                const actionsDiv = document.createElement('div');
                actionsDiv.className = 'history-actions';
                actionsDiv.innerHTML = `
                    <button class="resend-btn" onclick="resendOrder('${order.id}')">إعادة إرسال كتعديل</button>
                `;
                detailsDiv.appendChild(actionsDiv);
            }

            orderDiv.appendChild(headerDiv);
            orderDiv.appendChild(detailsDiv);
            container.appendChild(orderDiv);
        });

    } catch (error) {
        console.error('Error loading order history:', error);
        showToast('خطأ في تحميل الطلبات السابقة', 'error');
    }
}

// Resend order as modification
async function resendOrder(orderId) {
    try {
        const response = await fetch(`/api/waiter-orders/${orderId}/resend`, {
            method: 'POST'
        });

        const result = await response.json();

        if (result.success) {
            showToast('تم إعادة إرسال الطلب كتعديل', 'success');
            loadOrderHistory();
            loadPendingOrders();
        } else {
            showToast(result.error || 'فشل في إعادة الإرسال', 'error');
        }

    } catch (error) {
        console.error('Error resending order:', error);
        showToast('خطأ في إعادة الإرسال', 'error');
    }
}

// Show main sections and hide others
function showMainSections() {
    // Hide all sections
    document.querySelectorAll('.order-type-section, .table-selection, .delivery-info, .menu-section, .pending-orders, .order-history').forEach(section => {
        section.style.display = 'none';
    });

    // Show main sections
    document.getElementById('orderTypeSection').style.display = 'block';

    // Update navigation
    updateNavigation('main');
}

// Show specific section
function showSection(sectionId) {
    // Hide all sections
    document.querySelectorAll('.order-type-section, .table-selection, .delivery-info, .menu-section, .pending-orders, .order-history').forEach(section => {
        section.style.display = 'none';
    });

    // Show selected section
    document.getElementById(sectionId).style.display = 'block';

    // Load data based on section
    if (sectionId === 'pendingOrders') {
        loadPendingOrders();
        updateNavigation('pending');
    } else if (sectionId === 'orderHistory') {
        loadOrderHistory();
        updateNavigation('history');
    }
}

// Update navigation active state
function updateNavigation(activeSection) {
    // Remove active class from all nav items
    document.querySelectorAll('.nav-item').forEach(item => {
        item.classList.remove('active');
    });

    // Add active class to current section
    const navItems = document.querySelectorAll('.nav-item');
    if (activeSection === 'main') {
        navItems[0].classList.add('active');
    } else if (activeSection === 'pending') {
        navItems[1].classList.add('active');
    } else if (activeSection === 'history') {
        navItems[2].classList.add('active');
    }
}
