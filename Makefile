# نظام إدارة مطعم النوفوس - Makefile
# Restaurant Management System - Makefile
# 
# تطوير: شركة الباش البرمجية
# Developer: Al-Bash Software Company
# الهاتف/Phone: 07715086335
# واتساب/WhatsApp: 07715086335
# الموقع/Website: www.albash-software.com
# 
# © 2025 شركة الباش البرمجية. جميع الحقوق محفوظة.
# © 2025 Al-Bash Software Company. All rights reserved.

# Compiler and flags
CC = gcc
CFLAGS = -Wall -Wextra -std=c99
LIBS = -lws2_32
TARGET = system_launcher.exe
SOURCE = system_launcher.c

# Default target
all: $(TARGET)

# Build the executable
$(TARGET): $(SOURCE)
	@echo ========================================
	@echo    تجميع مشغل النظام - Compiling System Launcher
	@echo ========================================
	@echo.
	@echo شركة الباش البرمجية - Al-Bash Software Company
	@echo الهاتف/واتساب: 07715086335
	@echo Phone/WhatsApp: 07715086335
	@echo.
	@echo ========================================
	@echo.
	@echo تجميع البرنامج...
	@echo Compiling program...
	$(CC) $(CFLAGS) -o $(TARGET) $(SOURCE) $(LIBS)
	@echo.
	@echo ✅ تم تجميع البرنامج بنجاح!
	@echo ✅ Program compiled successfully!
	@echo.
	@echo الملف المنشأ: $(TARGET)
	@echo Generated file: $(TARGET)
	@echo.
	@echo للتشغيل: $(TARGET)
	@echo To run: $(TARGET)
	@echo.

# Clean build files
clean:
	@echo تنظيف الملفات...
	@echo Cleaning files...
	@if exist $(TARGET) del $(TARGET)
	@echo ✅ تم تنظيف الملفات
	@echo ✅ Files cleaned

# Install dependencies (if needed)
install:
	@echo فحص المتطلبات...
	@echo Checking requirements...
	@echo.
	@echo المتطلبات المطلوبة:
	@echo Required dependencies:
	@echo - GCC Compiler
	@echo - Windows SDK
	@echo - Winsock2 Library
	@echo.
	@echo إذا لم تكن متوفرة، يرجى تثبيت MinGW أو Visual Studio
	@echo If not available, please install MinGW or Visual Studio
	@echo.

# Run the program
run: $(TARGET)
	@echo تشغيل مشغل النظام...
	@echo Running system launcher...
	@echo.
	./$(TARGET)

# Help
help:
	@echo ========================================
	@echo    نظام إدارة مطعم النوفوس - مساعدة
	@echo    Restaurant Management System - Help
	@echo ========================================
	@echo.
	@echo الأوامر المتاحة / Available commands:
	@echo.
	@echo make          - تجميع البرنامج / Compile program
	@echo make all      - تجميع البرنامج / Compile program
	@echo make clean    - تنظيف الملفات / Clean files
	@echo make install  - فحص المتطلبات / Check requirements
	@echo make run      - تشغيل البرنامج / Run program
	@echo make help     - عرض المساعدة / Show help
	@echo.
	@echo شركة الباش البرمجية - Al-Bash Software Company
	@echo الهاتف/واتساب: 07715086335
	@echo Phone/WhatsApp: 07715086335
	@echo.

.PHONY: all clean install run help
