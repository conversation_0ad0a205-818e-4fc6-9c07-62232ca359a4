const express = require('express');
const fs = require('fs');
const path = require('path');
const moment = require('moment');
const { v4: uuidv4 } = require('uuid');
const { authenticateToken } = require('../middleware/auth');

const router = express.Router();

// Helper function to load orders data
const loadOrders = () => {
    try {
        const ordersPath = path.join(__dirname, '../data/orders.json');
        const data = fs.readFileSync(ordersPath, 'utf8');
        return JSON.parse(data);
    } catch (error) {
        console.error('Error loading orders:', error);
        return { orders: [] };
    }
};

// Helper function to save orders data
const saveOrders = (ordersData) => {
    try {
        const ordersPath = path.join(__dirname, '../data/orders.json');
        fs.writeFileSync(ordersPath, JSON.stringify(ordersData, null, 2));
        return true;
    } catch (error) {
        console.error('Error saving orders:', error);
        return false;
    }
};

// POST /api/orders - Create new order
router.post('/', authenticateToken, (req, res) => {
    try {
        const {
            type, // 'dine-in', 'takeaway', 'delivery'
            hallId,
            tableNumber,
            customerPhone,
            customerAddress,
            items,
            notes
        } = req.body;

        if (!type || !items || !Array.isArray(items) || items.length === 0) {
            return res.status(400).json({ error: 'Order type and items are required' });
        }

        // Validate order type
        if (!['dine-in', 'takeaway', 'delivery'].includes(type)) {
            return res.status(400).json({ error: 'Invalid order type' });
        }

        // Validate required fields based on order type
        if (type === 'dine-in' && (!hallId || !tableNumber)) {
            return res.status(400).json({ error: 'Hall ID and table number are required for dine-in orders' });
        }

        if (type === 'delivery' && (!customerPhone || !customerAddress)) {
            return res.status(400).json({ error: 'Customer phone and address are required for delivery orders' });
        }

        // Calculate total amount
        const totalAmount = items.reduce((sum, item) => {
            return sum + (item.price * item.quantity);
        }, 0);

        const ordersData = loadOrders();
        const newOrder = {
            id: uuidv4(),
            orderNumber: ordersData.orders.length + 1,
            type,
            hallId: type === 'dine-in' ? hallId : null,
            tableNumber: type === 'dine-in' ? tableNumber : null,
            customerPhone: type === 'delivery' ? customerPhone : null,
            customerAddress: type === 'delivery' ? customerAddress : null,
            items,
            totalAmount: parseFloat(totalAmount.toFixed(2)),
            notes: notes || '',
            status: 'pending',
            createdAt: moment().toISOString(),
            createdBy: req.user.id,
            waiterName: req.user.name,
            isModified: req.body.isModified || false,
            originalOrderNumber: req.body.originalOrderNumber || null
        };

        ordersData.orders.push(newOrder);

        if (saveOrders(ordersData)) {
            res.status(201).json({
                message: 'Order created successfully',
                order: newOrder
            });
        } else {
            res.status(500).json({ error: 'Failed to save order' });
        }

    } catch (error) {
        console.error('Error creating order:', error);
        res.status(500).json({ error: 'Failed to create order' });
    }
});

// GET /api/orders - Get orders with optional filters
router.get('/', authenticateToken, (req, res) => {
    try {
        const { type, date, status } = req.query;
        const ordersData = loadOrders();
        let filteredOrders = ordersData.orders;

        // Filter by type
        if (type) {
            filteredOrders = filteredOrders.filter(order => order.type === type);
        }

        // Filter by date
        if (date === 'today') {
            const today = moment().format('YYYY-MM-DD');
            filteredOrders = filteredOrders.filter(order => {
                return moment(order.createdAt).format('YYYY-MM-DD') === today;
            });
        }

        // Filter by status
        if (status) {
            filteredOrders = filteredOrders.filter(order => order.status === status);
        }

        // Sort by creation date (newest first)
        filteredOrders.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));

        res.json({
            orders: filteredOrders,
            total: filteredOrders.length
        });

    } catch (error) {
        console.error('Error fetching orders:', error);
        res.status(500).json({ error: 'Failed to fetch orders' });
    }
});

// GET /api/orders/:orderId - Get specific order
router.get('/:orderId', authenticateToken, (req, res) => {
    try {
        const orderId = req.params.orderId;
        const ordersData = loadOrders();
        
        const order = ordersData.orders.find(o => o.id === orderId);
        if (!order) {
            return res.status(404).json({ error: 'Order not found' });
        }

        res.json(order);

    } catch (error) {
        console.error('Error fetching order:', error);
        res.status(500).json({ error: 'Failed to fetch order' });
    }
});

// PUT /api/orders/:orderId/status - Update order status
router.put('/:orderId/status', authenticateToken, (req, res) => {
    try {
        const orderId = req.params.orderId;
        const { status } = req.body;

        if (!['pending', 'preparing', 'ready', 'completed', 'cancelled'].includes(status)) {
            return res.status(400).json({ error: 'Invalid order status' });
        }

        const ordersData = loadOrders();
        const order = ordersData.orders.find(o => o.id === orderId);

        if (!order) {
            return res.status(404).json({ error: 'Order not found' });
        }

        order.status = status;
        order.updatedAt = moment().toISOString();

        if (saveOrders(ordersData)) {
            res.json({
                message: 'Order status updated successfully',
                order
            });
        } else {
            res.status(500).json({ error: 'Failed to update order status' });
        }

    } catch (error) {
        console.error('Error updating order status:', error);
        res.status(500).json({ error: 'Failed to update order status' });
    }
});

// DELETE /api/orders/:orderId - Delete order
router.delete('/:orderId', authenticateToken, (req, res) => {
    try {
        const orderId = req.params.orderId;
        const ordersData = loadOrders();
        
        const orderIndex = ordersData.orders.findIndex(o => o.id === orderId);
        if (orderIndex === -1) {
            return res.status(404).json({ error: 'Order not found' });
        }

        ordersData.orders.splice(orderIndex, 1);

        if (saveOrders(ordersData)) {
            res.json({ message: 'Order deleted successfully' });
        } else {
            res.status(500).json({ error: 'Failed to delete order' });
        }

    } catch (error) {
        console.error('Error deleting order:', error);
        res.status(500).json({ error: 'Failed to delete order' });
    }
});

module.exports = router;
