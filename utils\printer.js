const ThermalPrinter = require('node-thermal-printer').printer;
const PrinterTypes = require('node-thermal-printer').types;
const fs = require('fs');
const path = require('path');
const moment = require('moment');

// Load settings
const loadSettings = () => {
    try {
        const settingsPath = path.join(__dirname, '../data/settings.json');
        const data = fs.readFileSync(settingsPath, 'utf8');
        return JSON.parse(data);
    } catch (error) {
        console.error('Error loading settings:', error);
        return null;
    }
};

// Save settings
const saveSettings = (settings) => {
    try {
        const settingsPath = path.join(__dirname, '../data/settings.json');
        fs.writeFileSync(settingsPath, JSON.stringify(settings, null, 2));
        return true;
    } catch (error) {
        console.error('Error saving settings:', error);
        return false;
    }
};

// Get available printers
const getAvailablePrinters = async () => {
    try {
        const printers = [];

        // Check for USB printers (Windows)
        if (process.platform === 'win32') {
            // Common USB printer paths on Windows
            const usbPrinters = [
                { name: 'USB Thermal Printer 1', path: 'USB001' },
                { name: 'USB Thermal Printer 2', path: 'USB002' },
                { name: 'USB Receipt Printer', path: 'USB003' }
            ];
            printers.push(...usbPrinters);
        } else {
            // Linux/Mac USB printer paths
            const usbPrinters = [
                { name: 'USB Thermal Printer 1', path: '/dev/usb/lp0' },
                { name: 'USB Thermal Printer 2', path: '/dev/usb/lp1' },
                { name: 'USB Receipt Printer', path: '/dev/usb/lp2' }
            ];
            printers.push(...usbPrinters);
        }

        // Check for network printers
        const networkPrinters = [
            { name: 'Network Printer 1', path: '*************' },
            { name: 'Network Printer 2', path: '*************' },
            { name: 'WiFi Thermal Printer', path: '*************' }
        ];
        printers.push(...networkPrinters);

        // Check for serial printers
        const serialPrinters = [
            { name: 'Serial Printer COM1', path: 'COM1' },
            { name: 'Serial Printer COM2', path: 'COM2' },
            { name: 'Serial Printer COM3', path: 'COM3' }
        ];
        printers.push(...serialPrinters);

        return printers;
    } catch (error) {
        console.error('Error getting printers:', error);
        return [];
    }
};

// Print receipt
const printReceipt = async (order, printerType = 'cashier') => {
    try {
        const settings = loadSettings();
        if (!settings) {
            throw new Error('Settings not found');
        }

        const printerPath = settings.printers[printerType];
        if (!printerPath) {
            throw new Error(`No printer assigned for ${printerType}`);
        }

        const printer = new ThermalPrinter({
            type: PrinterTypes.EPSON,
            interface: printerPath.startsWith('/dev') ? printerPath : `tcp://${printerPath}`,
            width: 48, // 88mm paper width
            characterSet: 'SLOVENIA',
            removeSpecialCharacters: false,
            lineCharacter: "-"
        });

        // Check if printer is connected
        const isConnected = await printer.isPrinterConnected();
        if (!isConnected) {
            throw new Error('Printer not connected');
        }

        // Build receipt content
        printer.alignCenter();
        printer.setTextSize(1, 1);
        printer.bold(true);
        printer.println(settings.restaurant.name);
        printer.bold(false);
        printer.println(settings.restaurant.phone);
        printer.println('');

        printer.alignLeft();
        printer.println(`التاريخ: ${moment(order.createdAt).format('YYYY-MM-DD HH:mm')}`);
        printer.println(`رقم الفاتورة: ${order.orderNumber}`);
        printer.println(`النادل: ${order.waiterName}`);

        // Order type specific info
        if (order.type === 'dine-in') {
            printer.println(`الصالة: ${order.hallName || 'غير محدد'}`);
            printer.println(`رقم الطاولة: ${order.tableNumber}`);
        } else if (order.type === 'takeaway') {
            printer.println('نوع الطلب: سفري');
        } else if (order.type === 'delivery') {
            printer.println('نوع الطلب: دلفري');
            printer.println(`رقم الزبون: ${order.customerPhone}`);
            printer.println(`العنوان: ${order.customerAddress}`);
        }

        printer.println('');
        printer.drawLine();

        // Items
        printer.println('الصنف        الكمية  السعر    المجموع');
        printer.drawLine();

        order.items.forEach(item => {
            const itemTotal = (item.price * item.quantity).toFixed(2);
            const line = `${item.name.padEnd(12)} ${item.quantity.toString().padStart(3)} x ${item.price.toFixed(2).padStart(5)} = ${itemTotal.padStart(7)}`;
            printer.println(line);
        });

        printer.drawLine();
        printer.alignRight();
        printer.setTextSize(1, 1);
        printer.bold(true);
        printer.println(`المجموع الكلي: ${order.totalAmount.toFixed(2)} دينار`);
        printer.bold(false);

        if (order.notes) {
            printer.println('');
            printer.alignLeft();
            printer.println(`ملاحظات: ${order.notes}`);
        }

        printer.println('');
        printer.alignCenter();
        printer.println('شكراً لزيارتكم');
        printer.println('');
        printer.cut();

        // Execute print
        await printer.execute();
        return { success: true, message: 'Receipt printed successfully' };

    } catch (error) {
        console.error('Print error:', error);
        return { success: false, error: error.message };
    }
};

// Print kitchen order
const printKitchenOrder = async (order) => {
    try {
        const settings = loadSettings();
        if (!settings) {
            throw new Error('Settings not found');
        }

        const printerPath = settings.printers.kitchen;
        if (!printerPath) {
            throw new Error('No kitchen printer assigned');
        }

        const printer = new ThermalPrinter({
            type: PrinterTypes.EPSON,
            interface: printerPath.startsWith('/dev') ? printerPath : `tcp://${printerPath}`,
            width: 48,
            characterSet: 'SLOVENIA',
            removeSpecialCharacters: false,
            lineCharacter: "-"
        });

        const isConnected = await printer.isPrinterConnected();
        if (!isConnected) {
            throw new Error('Kitchen printer not connected');
        }

        printer.alignCenter();
        printer.setTextSize(1, 1);
        printer.bold(true);
        printer.println('طلب المطبخ');
        printer.bold(false);
        printer.println('');

        printer.alignLeft();
        printer.println(`الوقت: ${moment(order.createdAt).format('HH:mm')}`);
        printer.println(`رقم الطلب: ${order.orderNumber}`);
        
        if (order.type === 'dine-in') {
            printer.println(`الطاولة: ${order.tableNumber}`);
        } else {
            printer.println(`النوع: ${order.type === 'takeaway' ? 'سفري' : 'دلفري'}`);
        }

        printer.println('');
        printer.drawLine();

        // Filter kitchen items (exclude drinks)
        const kitchenItems = order.items.filter(item => 
            !item.name.includes('شاي') && 
            !item.name.includes('قهوة') && 
            !item.name.includes('عصير')
        );

        kitchenItems.forEach(item => {
            printer.setTextSize(1, 1);
            printer.bold(true);
            printer.println(`${item.quantity} x ${item.name}`);
            printer.bold(false);
        });

        if (order.notes) {
            printer.println('');
            printer.println(`ملاحظات: ${order.notes}`);
        }

        printer.println('');
        printer.cut();

        await printer.execute();
        return { success: true, message: 'Kitchen order printed successfully' };

    } catch (error) {
        console.error('Kitchen print error:', error);
        return { success: false, error: error.message };
    }
};

module.exports = {
    getAvailablePrinters,
    printReceipt,
    printKitchenOrder,
    loadSettings,
    saveSettings
};
